{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended", "plugin:@angular-eslint/template/accessibility"], "rules": {"@angular-eslint/template/interactive-supports-focus": "off", "@angular-eslint/template/click-events-have-key-events": "off"}}, {"files": ["*.ts"], "extends": ["plugin:@ngrx/recommended-requiring-type-checking"]}]}