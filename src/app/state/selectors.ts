import { createSelector } from '@ngrx/store';
import dayjs from 'dayjs';
import { orderBy } from 'lodash';
import { INodesData } from '../api/api-sdk';
import { nodesFeature } from '../components/admin/nodes/state/nodes.feature';
import { IVisualMapDevice } from '../model/node';
import { adminFeature } from './admin/feature';

export const selectNodesDataWithLocalTime = createSelector(
  adminFeature.selectLocationTimezone,
  nodesFeature.selectNodesData,
  (timezone, nodesData) =>
    nodesData.map(
      (n) =>
        ({
          ...n,
          date: dayjs
            .unix(n.timestamp_unix)
            .tz(timezone)
            .format('DD MMM YYYY, HH:mm'),
        }) satisfies INodesData,
    ),
);

export const selectVisualMapDevices = createSelector(
  adminFeature.selectAllDevicesForActiveLocation,
  nodesFeature.selectActiveVisualMap,
  (devices, visualMap) =>
    devices.map(
      (d) =>
        ({
          ...d,
          position: visualMap?.data?.['devices']?.find(
            (vmd: any) => vmd.id === d.remote_id,
          ),
        }) satisfies IVisualMapDevice,
    ),
);

export const selectSortedDevicesForActiveLocation = createSelector(
  adminFeature.selectAllDevicesForActiveLocation,
  nodesFeature.selectNodesSortField,
  nodesFeature.selectNodesOrder,
  (devices, sortField, order) =>
    orderBy(
      devices,
      sortField.field === 'custom'
        ? (d) =>
            !!d.thermal_camera_id
              ? Number.MAX_VALUE
              : order.findIndex((id) => id === d.remote_id)
        : sortField.field,
      sortField.direction ?? 'asc',
    ),
);
