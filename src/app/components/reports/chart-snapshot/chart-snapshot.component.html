<ng-container *ngrxLet="chartData$ as chartData">
  @if (chartData) {
    <div class="flex flex-col h-screen">
      <div id="chart-legend" class="p-4">
        <app-chart-legend
          [chartData]="chartData"
          [readOnly]="true"
        ></app-chart-legend>
      </div>
      <div class="flex-1 px-2 overflow-hidden">
        <canvas
          id="chat-canvas"
          baseChart
          [data]="chartData.data"
          [options]="chartData.options"
          [type]="'line'"
          class="!w-full !h-full"
        >
        </canvas>
      </div>
    </div>
  }
</ng-container>
