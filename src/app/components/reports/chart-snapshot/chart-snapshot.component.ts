import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import 'chartjs-adapter-dayjs-4/dist/chartjs-adapter-dayjs-4.esm';
import { NgChartsModule } from 'ng2-charts';
import { Observable, map, tap } from 'rxjs';
import { IChartData, IChartReportData } from '../../../model/dashboard';
import { AdminActions } from '../../../state/admin/actions';
import { DashboardUtils } from '../../../utils/dashboard';
import { ChartLegendComponent } from '../../admin/dashboard/components/chart-legend/chart-legend.component';
import { DashboardActions } from '../../admin/dashboard/state/dashboard.actions';
import { dashboardFeature } from './../../admin/dashboard/state/dashboard.feature';

@Component({
  selector: 'app-chart-snapshot',
  standalone: true,
  imports: [CommonModule, NgChartsModule, ChartLegendComponent, LetDirective],
  templateUrl: './chart-snapshot.component.html',
  styleUrls: [],
})
export class ChartSnapshotComponent implements OnInit {
  chartData$!: Observable<IChartData>;

  constructor(
    private route: ActivatedRoute,
    private store: Store,
  ) {}

  async ngOnInit() {
    // http://localhost:4200/auth/explicitLogin?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQyODQ1NzA0LCJpYXQiOjE3NDI1MTA0MjQsImp0aSI6IjA5OTUyMjIyNzY0NDRhMjI5MDFiOTJkOWQyODI1NTI0IiwidXNlcl9pZCI6M30.22c5GyhN0OiA95RFoQIxw13zfzcHBn2pAmw0Habbn8U&returnUrl=/reports/chart-snapshot&data=%257B%2522centralId%2522%253A10503%252C%2522startTimestamp%2522%253A1742256000000%252C%2522endTimestamp%2522%253A1742515200000%252C%2522config%2522%253A%257B%2522devices%2522%253A%255B%257B%2522name%2522%253A%2522DryAir%252B%2520KAS1%2520achterin%2520de%2520rij(1138)%2520%257C%2520Rij%2520151%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A58161%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522DryAir%252B%2520KAS2%2520achter%2520-%2520achterin%2520de%2520rij%2520(1137)%2520%257C%2520Rij%2520281%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A41724%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522DryAir%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(1136)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A52225%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522Netradiometer%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(4523)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A72%252C%2522remote_id%2522%253A4523%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C12%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522SoilPro%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(63726)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A12%252C%2522remote_id%2522%253A63726%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C4%252C5%252C6%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%255D%252C%2522recognitions%2522%253A%255B%257B%2522cameraId%2522%253A1136%252C%2522recognitionType%2522%253A%2522flowers%2522%257D%255D%252C%2522recognitionVariables%2522%253A%255B%257B%2522uom%2522%253A%2522%25C2%25B0C%2522%252C%2522name%2522%253A%2522cameraTemperature%2522%252C%2522type%2522%253A%2522temperature%2522%257D%255D%252C%2522variables%2522%253A%255B%257B%2522id%2522%253A3%252C%2522name%2522%253A%2522temp%2522%252C%2522unit%2522%253A%2522%25C2%25B0C%2522%252C%2522full_name%2522%253A%2522Air%2520Temperature%2522%252C%2522customizable%2522%253A1%257D%252C%257B%2522id%2522%253A28%252C%2522name%2522%253A%2522vbat%2522%252C%2522unit%2522%253A%2522%2525%2522%252C%2522full_name%2522%253A%2522Battery%2520percentage%2522%252C%2522customizable%2522%253A1%257D%252C%257B%2522id%2522%253A20%252C%2522name%2522%253A%2522t_leaf%2522%252C%2522unit%2522%253A%2522%25C2%25B0C%2522%252C%2522full_name%2522%253A%2522Dry%2520Leaf%2520Temp%2522%252C%2522customizable%2522%253A1%257D%255D%252C%2522chartUomConfigs%2522%253A%257B%257D%257D%257D
    // https://dev.sigrow.com/auth/explicitLogin?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQyODQ4NjEzLCJpYXQiOjE3NDI1MDU5MDMsImp0aSI6ImJhYzI1OTJmZmFhZjQzMWZhNjNmYWIzODI0NmNhNTJhIiwidXNlcl9pZCI6M30.APH9w27tR_gJiarcuyv3BzhRvBy26huDgDGoiy3IpM0&returnUrl=/reports/chart-snapshot&data=%257B%2522centralId%2522%253A10503%252C%2522startTimestamp%2522%253A1742256000000%252C%2522endTimestamp%2522%253A1742515200000%252C%2522config%2522%253A%257B%2522devices%2522%253A%255B%257B%2522name%2522%253A%2522DryAir%252B%2520KAS1%2520achterin%2520de%2520rij(1138)%2520%257C%2520Rij%2520151%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A58161%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522DryAir%252B%2520KAS2%2520achter%2520-%2520achterin%2520de%2520rij%2520(1137)%2520%257C%2520Rij%2520281%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A41724%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522DryAir%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(1136)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A40%252C%2522remote_id%2522%253A52225%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522Netradiometer%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(4523)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A72%252C%2522remote_id%2522%253A4523%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C7%252C8%252C12%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%252C%257B%2522name%2522%253A%2522SoilPro%252B%2520KAS2%2520voor%2520-%2520achterin%2520de%2520rij%2520(63726)%2520%257C%2520Rij%2520231%2520H%2522%252C%2522msg_id%2522%253A12%252C%2522remote_id%2522%253A63726%252C%2522allowed_variables%2522%253A%255B1%252C2%252C3%252C4%252C5%252C6%252C7%252C8%252C14%252C15%252C19%252C21%252C27%252C28%252C31%255D%257D%255D%252C%2522recognitions%2522%253A%255B%257B%2522cameraId%2522%253A1136%252C%2522recognitionType%2522%253A%2522flowers%2522%257D%255D%252C%2522recognitionVariables%2522%253A%255B%257B%2522uom%2522%253A%2522%25C2%25B0C%2522%252C%2522name%2522%253A%2522cameraTemperature%2522%252C%2522type%2522%253A%2522temperature%2522%257D%255D%252C%2522variables%2522%253A%255B%257B%2522id%2522%253A3%252C%2522name%2522%253A%2522temp%2522%252C%2522unit%2522%253A%2522%25C2%25B0C%2522%252C%2522full_name%2522%253A%2522Air%2520Temperature%2522%252C%2522customizable%2522%253A1%257D%252C%257B%2522id%2522%253A28%252C%2522name%2522%253A%2522vbat%2522%252C%2522unit%2522%253A%2522%2525%2522%252C%2522full_name%2522%253A%2522Battery%2520percentage%2522%252C%2522customizable%2522%253A1%257D%252C%257B%2522id%2522%253A20%252C%2522name%2522%253A%2522t_leaf%2522%252C%2522unit%2522%253A%2522%25C2%25B0C%2522%252C%2522full_name%2522%253A%2522Dry%2520Leaf%2520Temp%2522%252C%2522customizable%2522%253A1%257D%255D%252C%2522chartUomConfigs%2522%253A%257B%257D%257D%257D

    const params = this.route.snapshot.queryParams;
    const data: IChartReportData = DashboardUtils.decodeChartReportDataFromUri(
      params['data'],
    );

    this.store.dispatch(
      AdminActions.activeLocationChanged({
        location: { central_id: data.centralId } as any,
      }),
    );

    this.store.dispatch(DashboardActions.applyChartReportData({ data }));

    this.chartData$ = this.store
      .select(dashboardFeature.selectChatJsBinding)
      .pipe(
        // eslint-disable-next-line @ngrx/avoid-mapping-selectors
        map((chartData) => {
          chartData.options = {
            ...chartData.options,
            animation: false,
          };
          return chartData;
        }),
        tap((chartData) => {
          if (
            !chartData.data.datasets.length ||
            document.getElementById('chart-render-complete')
          ) {
            return;
          }
          const chartRenderCompleteElement = document.createElement('div');
          chartRenderCompleteElement.setAttribute(
            'id',
            'chart-render-complete',
          );
          document.body.append(chartRenderCompleteElement);
        }),
      );

    setTimeout(() => {
      document.body.style.backgroundColor = '#fff';
      (window as any).FreshworksWidget('hide');
    });
  }
}
