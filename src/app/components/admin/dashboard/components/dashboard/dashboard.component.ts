import { CommonModule } from '@angular/common';
import { Component, NgZone, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { ChartEvent } from 'chart.js';
import 'chartjs-adapter-dayjs-4/dist/chartjs-adapter-dayjs-4.esm';
import dayjs from 'dayjs';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import {
  combineLatest,
  lastValueFrom,
  map,
  Observable,
  startWith,
  take,
  tap,
} from 'rxjs';
import {
  IChartsCommentItem,
  IGetCurrentCamPoint,
  IVariableToDisplay,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { AlphaPipe } from '../../../../../common/pipes/alpha.pipe';
import { DpDateFormatPipe } from '../../../../../common/pipes/dayjs/dp-date-format.pipe';
import { DpFromUnixMSPipe } from '../../../../../common/pipes/dayjs/dp-from-unix-ms.pipe';
import { DpFromUnixPipe } from '../../../../../common/pipes/dayjs/dp-from-unix.pipe';
import { DeviceNamePipe } from '../../../../../common/pipes/device-name.pipe';
import { PointNamePipe } from '../../../../../common/pipes/point-name.pipe';
import SharedModules from '../../../../../common/shared.modules';
import { ICameraImage } from '../../../../../model/camera';
import {
  ICameraVariable,
  IChartData,
  IChartLegendItem,
  IChartUomConfig,
  ICustomPoint,
  IDevice,
  ITooltip,
} from '../../../../../model/dashboard';
import { DateRange } from '../../../../../model/dateRange';
import { adminFeature } from '../../../../../state/admin/feature';
import { selectSortedDevicesForActiveLocation } from '../../../../../state/selectors';
import { ChartUtils } from '../../../../../utils/chart';
import { DeviceUtils } from '../../../../../utils/device';
import { DateRangePickerComponent } from '../../../../common/date-range-picker/date-range-picker.component';
import { DownloadButtonComponent } from '../../../../common/download-button/download-button.component';
import {
  DownloadDialogComponent,
  IDownloadConfig,
} from '../../../../common/download-dialog/download-dialog.component';
import { MobileSidenavMenuBtnComponent } from '../../../../common/mobile-sidenav-menu-btn/mobile-sidenav-menu-btn.component';
import { DeviceCustomPointsPipe } from '../../pipes/device-custom-points.pipe';
import { DevicePointsPipe } from '../../pipes/device-points.pipe';
import { DeviceSelectedPipe } from '../../pipes/device-selected.pipe';
import { DeviceSettingsPipe } from '../../pipes/device-settings.pipe';
import { PointSelectedPipe } from '../../pipes/point-selected.pipe';
import { PointVariableSelectedPipe } from '../../pipes/point-variable-selected.pipe';
import { RecognitionSelectedPipe } from '../../pipes/rec-selected.pipe';
import { RecognitionTypeSupportedPipe } from '../../pipes/rec-type-supported.pipe';
import { RecognitionVariableSelectedPipe } from '../../pipes/rec-variable-selected.pipe';
import { VariableSelectedPipe } from '../../pipes/variable-selected.pipe';
import { DashboardActions } from '../../state/dashboard.actions';
import { dashboardFeature } from '../../state/dashboard.feature';
import { CameraImageComponent } from '../camera-image/camera-image.component';
import { ChartLegendComponent } from '../chart-legend/chart-legend.component';
import { ChartUomDialogComponent } from '../chart-uom-dialog/chart-uom-dialog.component';
import { SavedConfigsDialogComponent } from '../saved-configs-dialog/saved-configs-dialog.component';
import { RecognitionType } from './../../../../../model/dashboard';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgChartsModule,
    CameraImageComponent,
    DownloadButtonComponent,
    DateRangePickerComponent,
    ChartLegendComponent,
    MobileSidenavMenuBtnComponent,
    LetDirective,
    DevicePointsPipe,
    DeviceSettingsPipe,
    DeviceNamePipe,
    DeviceCustomPointsPipe,
    DeviceSelectedPipe,
    VariableSelectedPipe,
    RecognitionVariableSelectedPipe,
    RecognitionSelectedPipe,
    RecognitionTypeSupportedPipe,
    PointSelectedPipe,
    PointVariableSelectedPipe,
    PointNamePipe,
    DpFromUnixPipe,
    DpFromUnixMSPipe,
    DpDateFormatPipe,
    AlphaPipe,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent extends BaseComponent implements OnInit {
  @ViewChild(BaseChartDirective) chart!: BaseChartDirective;

  devices$!: Observable<IDevice[]>;
  variables$!: Observable<IVariableToDisplay[]>;
  recognitionVariables$!: Observable<ICameraVariable[]>;
  pointVariables$!: Observable<ICameraVariable[]>;
  dateRange$!: Observable<DateRange>;
  chartData$!: Observable<IChartData>;
  cameraImagesGrouped$!: Observable<
    { cameraId: string; images: ICameraImage[] }[]
  >;
  groupedTooltips$!: Observable<{ title: string; tooltips: ITooltip[] }[]>;
  isAnyCameraSelected$!: Observable<boolean>;
  imagesDisplayed$!: Observable<boolean>;
  chartComments$!: Observable<IChartsCommentItem[]>;
  expandedChartComments$!: Observable<IChartsCommentItem[]>;
  chartRangeStats$!: Observable<Map<
    string,
    { min: number; max: number; avg: number }
  > | null>;
  chartRange$!: Observable<{
    startTimestamp: number;
    endTimestamp: number;
  } | null>;
  minimizeComments$!: Observable<boolean>;
  hideCondensation$!: Observable<boolean>;

  searchCtrl = new FormControl<string | undefined>(undefined);

  recognitionTypes = RecognitionType;

  displayToolip = false;
  chartPlaceholder: string | undefined = undefined;
  inZoom = false;
  expandCameraView = false;
  commentMode = false;
  showChartRangeStats = false;

  currentZoom:
    | Record<string, { min: number; max: number } | undefined>
    | undefined;

  constructor(
    private store: Store,
    private dialog: MatDialog,
    private zone: NgZone,
  ) {
    super();
  }

  ngOnInit() {
    this.initDashboard();
  }

  dateRangeChanged(dateRange: DateRange) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.dateRangeChanged({ dateRange }));
      this.resetZoom();
    });
  }

  shiftDateRange(delta: number) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.shiftDateRange({ delta }));
      this.resetZoom();
    });
  }

  toggleDevice(device: IDevice) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.toggleDevice({ device }));
    });
  }

  toggleVariable(variable: IVariableToDisplay) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.toggleVariable({ variable }));
    });
  }

  toggleRecognitionVariable(variable: ICameraVariable) {
    this.runChartAction(() => {
      this.store.dispatch(
        DashboardActions.toggleRecognitionVariable({ variable }),
      );
    });
  }

  togglePointVariable(variable: ICameraVariable) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.togglePointVariable({ variable }));
    });
  }

  togglePoint(point: IGetCurrentCamPoint) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.togglePoint({ point }));
    });
  }

  toggleCustomPoint(customPoint: ICustomPoint) {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.toggleCustomPoint({ customPoint }));
    });
  }

  toggleRecognition(cameraId: number, recognitionType: RecognitionType) {
    this.runChartAction(() => {
      this.store.dispatch(
        DashboardActions.toggleRecognition({
          config: { cameraId, recognitionType },
        }),
      );
    });
  }

  toggleImagesDisplay() {
    this.store.dispatch(DashboardActions.toggleImagesDisplay());
  }

  refresh() {
    this.runChartAction(() => {
      this.store.dispatch(DashboardActions.refresh());
    });
  }

  onChartHover(chartEvent: {
    event?: ChartEvent | undefined;
    active?: object[] | undefined;
  }) {
    const chartCoords = ChartUtils.getChartPointCoordinates(
      this.chart.chart as never,
      chartEvent,
    );

    if (!chartCoords.chartX || !chartCoords.chartY) {
      return;
    }

    const { clientX, clientY } = chartEvent.event?.native as MouseEvent;

    this.store.dispatch(
      DashboardActions.chartHover({
        timestamp: chartCoords.chartX,
        clientX,
        clientY,
      }),
    );
  }

  onChartClick(chartEvent: {
    event?: ChartEvent | undefined;
    active?: object[] | undefined;
  }) {
    if (!this.commentMode) {
      return;
    }

    const chartCoords = ChartUtils.getChartPointCoordinates(
      this.chart.chart as never,
      chartEvent,
    );

    if (!chartCoords.chartX || !chartCoords.chartY) {
      return;
    }

    this.store.dispatch(
      DashboardActions.chartClick({
        chartX: chartCoords.chartX,
        chartY: chartCoords.chartY,
      }),
    );

    this.commentMode = false;
    this.inZoom = false;
  }

  async openLegendItemDetails(
    legendItem: IChartLegendItem,
    uomConfig: IChartUomConfig,
  ) {
    const dialogRef = this.dialog.open(ChartUomDialogComponent, {
      data: uomConfig,
    });
    const config = await lastValueFrom(dialogRef.afterClosed());
    if (config) {
      this.store.dispatch(
        DashboardActions.chartUomConfigChanged({ uom: legendItem.uom, config }),
      );
    }
  }

  async openDownloadDialog() {
    const dateRange = await lastValueFrom(this.dateRange$.pipe(take(1)));

    this.dialog.open(DownloadDialogComponent, {
      data: {
        startDate: dateRange.start().toDate(),
        endDate: dateRange.end().toDate(),
        preSelectedDevices: this.store.select(dashboardFeature.selectDevices),
        preSelectedVariables: this.store.select(
          dashboardFeature.selectVariables,
        ),
        preSelectedPoints: this.store.select(dashboardFeature.selectPoints),
      } satisfies IDownloadConfig,
    });
  }

  openSavedConfigsDialog() {
    this.dialog.open(SavedConfigsDialogComponent);
  }

  toggleCommentMode() {
    this.commentMode = !this.commentMode;
  }

  toggleCommentsDisplay() {
    this.store.dispatch(DashboardActions.toggleMinimizedComments());
  }

  async deleteChartComment(comment: IChartsCommentItem) {
    if (!(await this.ui.confirm())) {
      return;
    }

    this.store.dispatch(
      DashboardActions.chartCommentDeleted({ id: comment.id }),
    );
  }

  focusChartComment(comment: IChartsCommentItem) {
    this.store.dispatch(DashboardActions.focusChartComment({ comment }));
  }

  async downloadChartImage() {
    this.store.dispatch(DashboardActions.downloadChartImage());
  }

  toggleChartRangeStats() {
    this.showChartRangeStats = !this.showChartRangeStats;
    this.recalculateChartRangeStats();
  }

  toggleCondensation() {
    this.store.dispatch(DashboardActions.toggleCondensation());
  }

  resetZoom() {
    this.chart.chart?.resetZoom();
    this.currentZoom = undefined;
  }

  private runChartAction(action: () => void) {
    this.chartPlaceholder = this.chart?.chart?.toBase64Image();
    action();
  }

  private initDashboard() {
    this.devices$ = combineLatest([
      this.store.select(selectSortedDevicesForActiveLocation),
      this.searchCtrl.valueChanges.pipe(startWith(null)),
    ]).pipe(
      map(([devices, search]) =>
        search
          ? devices.filter(
            (d) =>
              DeviceUtils.getDeviceName(d)
                ?.toLowerCase()
                .indexOf(search.toLowerCase()) >= 0 ||
              d.remote_id.toString().startsWith(search),
          )
          : devices,
      ),
    );
    this.chartData$ = this.store
      .select(dashboardFeature.selectChatJsBinding)
      .pipe(
        tap(() => {
          if (!this.currentZoom) {
            return;
          }

          for (const [scale, range] of Object.entries(this.currentZoom)) {
            if (range) {
              setTimeout(() => {
                this.chart.chart?.zoomScale(scale.toString(), range);
              });
            }
          }
        }),
        map((chartData) => {
          const zoomConfig = chartData.options?.plugins?.zoom?.zoom;

          if (zoomConfig) {
            zoomConfig.onZoomStart = () => {
              this.zone.run(() => {
                this.inZoom = true;
              });
              return true;
            };
            zoomConfig.onZoomComplete = () => {
              this.zone.run(() => {
                this.inZoom = false;
                this.currentZoom = this.chart.chart?.getZoomedScaleBounds();
                if (
                  this.currentZoom &&
                  this.currentZoom['x'] &&
                  this.currentZoom['y']
                ) {
                  this.recalculateChartRangeStats();
                }
              });
              return true;
            };
            zoomConfig.onZoomRejected = () => {
              this.zone.run(() => {
                this.inZoom = false;
              });
              return true;
            };
          }

          return chartData;
        }),
        tap(() => this.recalculateChartRangeStats()),
        tap((chartData) => {
          if (!this.chartPlaceholder) {
            return;
          }

          const hasRecognitions = chartData.data.datasets.some(
            (dataset) => !!dataset.recType && !!dataset.recValueType,
          );

          setTimeout(
            () => {
              this.chartPlaceholder = undefined;
            },
            hasRecognitions ? 1000 : 300,
          );
        }),
      );
    this.variables$ = this.store.select(adminFeature.selectVariables);
    this.recognitionVariables$ = this.store.select(
      dashboardFeature.selectRecognitionVariables,
    );
    this.pointVariables$ = this.store.select(
      dashboardFeature.selectPointVariables,
    );
    this.dateRange$ = this.store.select(dashboardFeature.selectDateRange);
    this.cameraImagesGrouped$ = this.store.select(
      dashboardFeature.selectCameraImagesGrouped(),
    );
    this.groupedTooltips$ = this.store.select(
      dashboardFeature.selectGroupedTooltips,
    );
    this.isAnyCameraSelected$ = this.store.select(
      dashboardFeature.isAnyCameraSelected,
    );
    this.imagesDisplayed$ = this.store.select(
      dashboardFeature.selectImagesDisplayed,
    );
    this.chartComments$ = this.store.select(
      dashboardFeature.selectChartComments,
    );
    this.expandedChartComments$ = this.store.select(
      dashboardFeature.selectExpandedChartComments,
    );
    this.chartRangeStats$ = this.store.select(
      dashboardFeature.selectChartRangeStats,
    );
    this.chartRange$ = this.store.select(dashboardFeature.selectChartRange);
    this.minimizeComments$ = this.store.select(
      dashboardFeature.selectMinimizeComments,
    );
    this.hideCondensation$ = this.store.select(
      dashboardFeature.selectHideCondensation,
    );
    return true;
  }

  private recalculateChartRangeStats() {
    if (!this.showChartRangeStats || !this.chart.chart) {
      return;
    }

    // Try to get zoomed bounds first
    const bounds = this.chart.chart.getZoomedScaleBounds();

    let startTimestamp: number;
    let endTimestamp: number;

    if (bounds && bounds['x']) {
      // Chart is zoomed
      startTimestamp = Math.floor(bounds['x'].min);
      endTimestamp = Math.floor(bounds['x'].max);
    } else {
      // Chart is not zoomed - get full chart bounds
      const scales = this.chart.chart.scales;
      if (scales && scales['x']) {
        startTimestamp = Math.floor(scales['x'].min);
        endTimestamp = Math.floor(scales['x'].max);
      } else {
        return; // No valid scale data available
      }
    }

    console.log(
      `${dayjs.unix(startTimestamp / 1000).toString()}-${dayjs.unix(endTimestamp / 1000).toString()}`,
    );

    this.store.dispatch(
      DashboardActions.calculateChartRangeStats({
        startTimestamp,
        endTimestamp,
      }),
    );
  }
}
