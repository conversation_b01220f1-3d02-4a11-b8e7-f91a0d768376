<ng-container *ngrxLet="ui.isMobile$ as isMobile">
  <ng-container *ngrxLet="ui.isLandscape$ as isLandscape">
    <app-mobile-sidenav-menu-btn
      [sidenav]="sidenav"
    ></app-mobile-sidenav-menu-btn>
    <mat-sidenav-container class="h-full">
      <mat-sidenav
        #sidenav
        [mode]="isMobile ? 'over' : 'side'"
        [opened]="!isMobile"
        class="sidenav w-70 !bg-black !dark:bg-boxdark"
      >
        <div class="p-4 pb-1">
          <mat-form-field class="w-full">
            <mat-label>{{ "search" | translate }}</mat-label>
            <input matInput [formControl]="searchCtrl" />
            @if (searchCtrl.value) {
              <button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="searchCtrl.setValue(undefined)"
              >
                <mat-icon>close</mat-icon>
              </button>
            }
          </mat-form-field>
          <ng-container *ngrxLet="devices$ as devices">
            @for (device of devices; track device) {
              <ng-container *ngrxLet="device | deviceSelected as selected">
                <mat-expansion-panel
                  [expanded]="selected"
                  (opened)="selected ? undefined : toggleDevice(device)"
                  (closed)="selected ? toggleDevice(device) : undefined"
                  hideToggle
                  [ngClass]="{
                    'hover:!border-gray': !selected
                  }"
                  class="!bg-transparent !mb-2 !shadow-none border border-solid border-black"
                >
                  <mat-expansion-panel-header
                    [ngClass]="{
                      'bg-primary': selected,
                      'hover:!bg-graydark dark:hover:!bg-meta-4': !selected
                    }"
                    class="!h-auto !px-4 !py-2"
                  >
                    <mat-panel-title
                      [ngClass]="{ 'font-medium': selected }"
                      class="!m-0 !text-bodydark1 !tracking-wider"
                    >
                      @if (device.thermal_camera_id) {
                        <mat-icon class="me-2">camera</mat-icon>
                      }
                      {{ device | deviceName }}
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  @if (device.thermal_camera_id) {
                    <div
                      [ngClass]="{ 'bg-slate-100 rounded mt-0.5': selected }"
                      class="flex flex-col pt-2 px-4 pb-4"
                    >
                      <ng-container
                        *ngrxLet="device | deviceSettings as deviceSettings"
                      >
                        @if (
                          deviceSettings
                            | recTypeSupported: recognitionTypes.flowers
                        ) {
                          <ng-container
                            *ngTemplateOutlet="
                              recognitionToggleTmpl;
                              context: {
                                title: 'flowersRecognition',
                                cameraId: device.thermal_camera_id,
                                type: recognitionTypes.flowers
                              }
                            "
                          ></ng-container>

                          <mat-divider></mat-divider>
                        }

                        @if (
                          deviceSettings
                            | recTypeSupported: recognitionTypes.leaves
                        ) {
                          <ng-container
                            *ngTemplateOutlet="
                              recognitionToggleTmpl;
                              context: {
                                title: 'leavesRecognition',
                                cameraId: device.thermal_camera_id,
                                type: recognitionTypes.leaves
                              }
                            "
                          ></ng-container>

                          <mat-divider></mat-divider>
                        }

                        @if (
                          deviceSettings
                            | recTypeSupported: recognitionTypes.fruits
                        ) {
                          <ng-container
                            *ngTemplateOutlet="
                              recognitionToggleTmpl;
                              context: {
                                title: 'fruitsRecognition',
                                cameraId: device.thermal_camera_id,
                                type: recognitionTypes.fruits
                              }
                            "
                          ></ng-container>

                          <mat-divider></mat-divider>
                        }

                        @if (
                          deviceSettings
                            | recTypeSupported: recognitionTypes.heads
                        ) {
                          <ng-container
                            *ngTemplateOutlet="
                              recognitionToggleTmpl;
                              context: {
                                title: 'headsRecognition',
                                cameraId: device.thermal_camera_id,
                                type: recognitionTypes.heads
                              }
                            "
                          ></ng-container>

                          <mat-divider></mat-divider>
                        }
                      </ng-container>
                      <ng-container
                        *ngrxLet="device | devicePoints as devicePoints"
                      >
                        @for (
                          point of devicePoints;
                          track point.id;
                          let last = $last
                        ) {
                          <div
                            [ngClass]="{ 'mb-4': !last, 'mb-2': last }"
                            class="flex ms-1 mt-4"
                          >
                            <ng-container
                              *ngrxLet="point | pointSelected as selected"
                            >
                              <mat-slide-toggle
                                [checked]="selected"
                                (click)="togglePoint(point)"
                                color="primary"
                              >
                              </mat-slide-toggle>
                              <div class="flex-1 ms-2">
                                {{ point | pointName | async }}
                              </div>
                              <div
                                [style.background]="point.color"
                                class="mx-2 w-4 h-4 rounded"
                              ></div>
                            </ng-container>
                          </div>
                          @if (!last) {
                            <mat-divider></mat-divider>
                          }
                        }
                      </ng-container>
                      <ng-container
                        *ngrxLet="
                          device | deviceCustomPoints as deviceCustomPoints
                        "
                      >
                        @if (deviceCustomPoints?.length) {
                          <div class="mt-6 mb-2 text-base font-semibold">
                            Custom points
                          </div>
                        }
                        @for (
                          customPoint of deviceCustomPoints;
                          track customPoint;
                          let last = $last
                        ) {
                          <div class="flex mt-1 mb-1">
                            <button
                              mat-icon-button
                              (click)="toggleCustomPoint(customPoint)"
                              color="warn"
                              class="!p-2 !w-10 !h-10"
                            >
                              <mat-icon>delete</mat-icon>
                            </button>
                            <div class="flex items-center ms-3">
                              {{ "point" | translate }}
                              {{ customPoint.clientX }}x{{
                                customPoint.clientY
                              }}
                            </div>
                          </div>
                          @if (!last) {
                            <mat-divider></mat-divider>
                          }
                        }
                      </ng-container>
                    </div>
                  }
                </mat-expansion-panel>
              </ng-container>
            }
          </ng-container>
        </div>
      </mat-sidenav>
      <mat-sidenav-content class="cameras-drawer">
        <ng-container *ngrxLet="imagesDisplayed$ as imagesDisplayed">
          <mat-drawer-container class="w-full h-full p-4">
            @if (expandCameraView) {
              <mat-drawer
                [opened]="imagesDisplayed"
                [mode]="isMobile ? 'over' : 'side'"
                position="end"
                class="cameras-drawer w-[600px] max-w-[90%] !bg-white"
              >
                <ng-container
                  *ngTemplateOutlet="expandCameraViewTmpl"
                ></ng-container>
                <ng-container
                  *ngrxLet="cameraImagesGrouped$ as cameraImagesGrouped"
                >
                  @for (group of cameraImagesGrouped; track group.cameraId) {
                    <div class="flex flex-wrap -mx-3 px-6">
                      @for (
                        image of group.images;
                        track image.camera.thermal_camera_id + image.viewType
                      ) {
                        <div class="px-3 mb-6">
                          <app-camera-image
                            [image]="image"
                            [width]="523"
                            [height]="(523 * 3) / 4"
                          ></app-camera-image>
                        </div>
                      }
                    </div>
                  }
                </ng-container>
              </mat-drawer>
            } @else {
              <mat-drawer
                [opened]="imagesDisplayed"
                [mode]="isMobile ? 'over' : 'side'"
                position="end"
                class="cameras-drawer w-80 !bg-white"
              >
                <ng-container
                  *ngTemplateOutlet="expandCameraViewTmpl"
                ></ng-container>
                <ng-container
                  *ngrxLet="cameraImagesGrouped$ as cameraImagesGrouped"
                >
                  @for (group of cameraImagesGrouped; track group.cameraId) {
                    <div class="flex flex-wrap -mx-3 px-6">
                      @for (
                        image of group.images;
                        track image.camera.thermal_camera_id + image.viewType
                      ) {
                        <div class="px-3 mb-6">
                          <app-camera-image [image]="image"></app-camera-image>
                        </div>
                      }
                    </div>
                  }
                </ng-container>
              </mat-drawer>
            }
            <mat-drawer-content>
              <ng-container *ngrxLet="chartData$ as chartData">
                <div class="flex flex-col h-full overflow-x-hidden">
                  @if (isMobile) {
                    <div class="flex w-full mb-4">
                      <button
                        mat-flat-button
                        [matMenuTriggerFor]="variablesMenu"
                        color="primary"
                        class="flex-1 me-4"
                      >
                        {{ "variables" | translate }}
                      </button>
                      <button
                        mat-flat-button
                        [disabled]="!chartData.metadata?.legend?.length"
                        [matMenuTriggerFor]="legendMenu"
                        color="primary"
                        class="flex-1"
                      >
                        {{ "legend" | translate }}
                      </button>
                    </div>

                    <mat-menu #variablesMenu="matMenu">
                      <div class="px-4 pt-2">
                        <ng-container
                          *ngTemplateOutlet="variablesListTmpl"
                        ></ng-container>
                      </div>
                    </mat-menu>
                    <mat-menu #legendMenu="matMenu">
                      <div class="ps-4 pe-1 pt-2">
                        <app-chart-legend
                          [chartData]="chartData"
                          (onOpenLegendItemDetails)="
                            openLegendItemDetails(
                              $event.legendItem,
                              $event.uomConfig
                            )
                          "
                        ></app-chart-legend>
                      </div>
                    </mat-menu>
                  } @else {
                    <div class="mb-2">
                      <ng-container
                        *ngTemplateOutlet="variablesListTmpl"
                      ></ng-container>
                    </div>
                    <div>
                      <app-chart-legend
                        [chartData]="chartData"
                        (onOpenLegendItemDetails)="
                          openLegendItemDetails(
                            $event.legendItem,
                            $event.uomConfig
                          )
                        "
                      ></app-chart-legend>
                    </div>
                  }

                  <div class="flex-1">
                    <mat-grid-list
                      cols="4"
                      rowHeight="fit"
                      gutterSize="16"
                      [ngClass]="{
                        'h-[100vh]': isMobile && isLandscape,
                        'h-full': !isMobile || !isLandscape
                      }"
                    >
                      <mat-grid-tile colspan="4">
                        @if (!chartData?.data?.datasets?.length) {
                          <div
                            class="absolute bottom-0 flex flex-col items-center justify-center bg-white w-full h-[calc(100%-60px)] z-99 rounded text-complementary"
                          >
                            <mat-icon class="text-8xl !w-24 !h-24">
                              query_stats
                            </mat-icon>
                            <div class="text-2xl text-center font-medium mt-4">
                              {{ "selectDeviceAndVariable" | translate }}
                            </div>
                          </div>
                        }

                        <div
                          class="px-4 pt-4 py-5 relative w-full h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
                        >
                          <div class="flex mb-2">
                            <ng-container
                              *ngrxLet="
                                isAnyCameraSelected$ as isAnyCameraSelected
                              "
                            >
                              @if (!isMobile) {
                                <div class="mr-4">
                                  <ng-container
                                    *ngTemplateOutlet="dateRangePickerTmpl"
                                  ></ng-container>
                                </div>

                                @if (chartData?.data?.datasets?.length) {
                                  <button
                                    mat-flat-button
                                    [matTooltip]="'resetZoom' | translate"
                                    (click)="resetZoom()"
                                    class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>zoom_out</mat-icon>
                                    </div>
                                  </button>

                                  <button
                                    mat-flat-button
                                    [ngClass]="{
                                      '!bg-blue-500': commentMode,
                                      '!bg-stroke': !commentMode
                                    }"
                                    [matTooltip]="'addChartComment' | translate"
                                    (click)="toggleCommentMode()"
                                    class="mr-4 !px-2 !min-w-fit z-1"
                                  >
                                    <div
                                      [ngClass]="{ 'text-white': commentMode }"
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>rate_review</mat-icon>
                                    </div>
                                  </button>

                                  <ng-container
                                    *ngrxLet="
                                      minimizeComments$ as minimizeComments
                                    "
                                  >
                                    <button
                                      mat-flat-button
                                      [ngClass]="{
                                        '!bg-blue-500': minimizeComments,
                                        '!bg-stroke': !minimizeComments
                                      }"
                                      [matTooltip]="
                                        'minimizeComments' | translate
                                      "
                                      (click)="toggleCommentsDisplay()"
                                      class="mr-4 !px-2 !min-w-fit z-1"
                                    >
                                      <div
                                        [ngClass]="{
                                          'text-white': minimizeComments
                                        }"
                                        class="flex items-center justify-center text-gray-700"
                                      >
                                        <mat-icon>speaker_notes_off</mat-icon>
                                      </div>
                                    </button>
                                  </ng-container>

                                  <ng-container
                                    *ngrxLet="chartComments$ as chartComments"
                                  >
                                    <button
                                      mat-flat-button
                                      [matMenuTriggerFor]="commentsMenu"
                                      [matTooltip]="'comments' | translate"
                                      [disabled]="!chartComments.length"
                                      class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                    >
                                      <div
                                        class="flex items-center justify-center text-gray-700"
                                      >
                                        <mat-icon>chat</mat-icon>
                                      </div>
                                    </button>
                                    <mat-menu
                                      #commentsMenu="matMenu"
                                      xPosition="before"
                                      yPosition="above"
                                    >
                                      @for (
                                        comment of chartComments;
                                        track comment.id;
                                        let last = $last
                                      ) {
                                        <div
                                          class="px-4 py-2 text-graydark flex items-center"
                                        >
                                          <div class="flex-1 overflow-auto">
                                            <div
                                              class="line-clamp-3 text-ellipsis"
                                            >
                                              {{ comment.comment }}
                                            </div>
                                            <div
                                              class="text-sm mt-1 flex items-center"
                                            >
                                              <div
                                                [style.background-color]="
                                                  comment.color
                                                "
                                                class="w-4 h-4 border rounded-full mr-2"
                                              ></div>
                                              <div>
                                                {{
                                                  comment.unix_timestamp
                                                    | dpFromUnix
                                                    | dpDateFormat
                                                      : "DD/MM/YYYY HH:mm:ss"
                                                }}
                                              </div>
                                            </div>
                                          </div>
                                          <div class="flex">
                                            <button
                                              mat-icon-button
                                              [matMenuTriggerFor]="
                                                commentActions
                                              "
                                              (click)="$event.stopPropagation()"
                                            >
                                              <mat-icon>more_vert</mat-icon>
                                            </button>
                                            <mat-menu #commentActions="matMenu">
                                              <button
                                                mat-menu-item
                                                (click)="
                                                  focusChartComment(comment)
                                                "
                                              >
                                                <mat-icon>query_stats</mat-icon>
                                                <span>{{
                                                  "displayOnChart" | translate
                                                }}</span>
                                              </button>
                                              <button
                                                mat-menu-item
                                                (click)="
                                                  deleteChartComment(comment)
                                                "
                                              >
                                                <mat-icon>delete</mat-icon>
                                                <span>{{
                                                  "deleteComment" | translate
                                                }}</span>
                                              </button>
                                            </mat-menu>
                                          </div>
                                        </div>
                                        @if (!last) {
                                          <mat-divider></mat-divider>
                                        }
                                      }
                                    </mat-menu>
                                  </ng-container>

                                  <button
                                    mat-flat-button
                                    [matTooltip]="'refreshChart' | translate"
                                    (click)="refresh()"
                                    class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>refresh</mat-icon>
                                    </div>
                                  </button>

                                  <app-download-button
                                    [toolbar]="true"
                                    (openDownloadDialog)="openDownloadDialog()"
                                  ></app-download-button>

                                  <button
                                    mat-flat-button
                                    [disabled]="!isAnyCameraSelected"
                                    [matTooltip]="'camera' | translate"
                                    (click)="toggleImagesDisplay()"
                                    class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>camera</mat-icon>
                                    </div>
                                  </button>

                                  <button
                                    mat-flat-button
                                    (click)="downloadChartImage()"
                                    [matTooltip]="'download' | translate"
                                    class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>image</mat-icon>
                                    </div>
                                  </button>

                                  <button
                                    mat-flat-button
                                    (click)="toggleChartRangeStats()"
                                    [matTooltip]="'rangeDetails' | translate"
                                    class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>view_list</mat-icon>
                                    </div>
                                  </button>

                                  <ng-container
                                    *ngrxLet="
                                      hideCondensation$ as hideCondensation
                                    "
                                  >
                                    <button
                                      mat-flat-button
                                      (click)="toggleCondensation()"
                                      [matTooltip]="
                                        'toggleCondensationAnalysis' | translate
                                      "
                                      class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                    >
                                      <div
                                        class="flex items-center justify-center text-gray-700"
                                      >
                                        <mat-icon>
                                          @if (hideCondensation) {
                                            humidity_high
                                          } @else {
                                            humidity_low
                                          }
                                        </mat-icon>
                                      </div>
                                    </button>
                                  </ng-container>
                                }
                                <button
                                  mat-flat-button
                                  (click)="openSavedConfigsDialog()"
                                  [matTooltip]="'favorite' | translate"
                                  class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
                                >
                                  <div
                                    class="flex items-center justify-center text-gray-700"
                                  >
                                    <mat-icon>star</mat-icon>
                                  </div>
                                </button>
                              } @else {
                                <div class="w-full flex mb-2">
                                  <div class="mr-4 flex-grow">
                                    <ng-container
                                      *ngTemplateOutlet="dateRangePickerTmpl"
                                    ></ng-container>
                                  </div>
                                  <button
                                    [matMenuTriggerFor]="mobileToolbarMenu"
                                    mat-flat-button
                                    class="!px-2 !min-w-fit !bg-stroke z-1"
                                  >
                                    <div
                                      class="flex items-center justify-center text-gray-700"
                                    >
                                      <mat-icon>menu</mat-icon>
                                    </div>
                                  </button>
                                  <mat-menu #mobileToolbarMenu="matMenu">
                                    @if (chartData?.data?.datasets?.length) {
                                      <button
                                        mat-menu-item
                                        (click)="chart.chart?.resetZoom()"
                                      >
                                        {{ "resetZoom" | translate }}
                                      </button>
                                      <button mat-menu-item (click)="refresh()">
                                        {{ "refreshChart" | translate }}
                                      </button>
                                      <button
                                        mat-menu-item
                                        [disabled]="!isAnyCameraSelected"
                                        (click)="toggleImagesDisplay()"
                                      >
                                        {{ "camera" | translate }}
                                      </button>
                                      <button
                                        mat-menu-item
                                        (click)="downloadChartImage()"
                                      >
                                        {{ "download" | translate }}
                                      </button>
                                    }
                                    <button
                                      mat-menu-item
                                      (click)="openSavedConfigsDialog()"
                                    >
                                      {{ "favorite" | translate }}
                                    </button>
                                  </mat-menu>
                                </div>
                              }
                            </ng-container>
                          </div>
                          @if (chartPlaceholder) {
                            <img
                              [src]="chartPlaceholder"
                              alt="chartPlaceholder"
                              class="absolute left-0 bottom-[4px] w-full h-full px-4 pb-5 pt-16"
                            />
                          }
                          <ng-container
                            *ngrxLet="{
                              stats: chartRangeStats$,
                              range: chartRange$
                            } as rangeData"
                          >
                            @if (
                              rangeData.stats && showChartRangeStats && !inZoom
                            ) {
                              <div
                                class="absolute bottom-16 right-4 flex flex-col items-start mr-4 py-1 ps-3 rounded border-gray bg-gray"
                              >
                                <button
                                  mat-icon-button
                                  class="!absolute right-0 -top-10 !p-2 !w-10 !h-10"
                                  (click)="toggleChartRangeStats()"
                                >
                                  <mat-icon class="!text-slate-500"
                                    >close</mat-icon
                                  >
                                </button>
                                <div
                                  class="max-h-75 pe-3 overflow-x-hidden overflow-y-scroll"
                                >
                                  <table
                                    class="w-full text-sm text-left text-gray-500"
                                  >
                                    <thead
                                      class="text-xs text-gray-700 uppercase bg-gray-50"
                                    >
                                      <tr>
                                        <th
                                          colspan="4"
                                          class="text-center border-b border-b-slate-300 py-2"
                                        >
                                          {{
                                            rangeData.range!.startTimestamp
                                              | dpFromUnixMS
                                              | dpDateFormat
                                                : "DD/MM/YYYY HH:mm:ss"
                                          }}
                                          -
                                          {{
                                            rangeData.range!.endTimestamp
                                              | dpFromUnixMS
                                              | dpDateFormat
                                                : "DD/MM/YYYY HH:mm:ss"
                                          }}
                                        </th>
                                      </tr>
                                      <tr>
                                        <th class="pt-2">Device</th>
                                        <th class="text-right ps-4 pt-2">
                                          Min
                                        </th>
                                        <th class="text-right ps-4 pt-2">
                                          Avg
                                        </th>
                                        <th class="text-right ps-4 pt-2">
                                          Max
                                        </th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      @for (
                                        entry of rangeData.stats | keyvalue;
                                        track entry.key
                                      ) {
                                        <tr>
                                          <td>
                                            {{ entry.key | translate }}
                                          </td>
                                          <td class="text-right ps-4">
                                            {{
                                              entry.value.min | number: "1.2-2"
                                            }}
                                          </td>
                                          <td class="text-right ps-4">
                                            {{
                                              entry.value.avg | number: "1.2-2"
                                            }}
                                          </td>
                                          <td class="text-right ps-4">
                                            {{
                                              entry.value.max | number: "1.2-2"
                                            }}
                                          </td>
                                        </tr>
                                      }
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            }
                          </ng-container>
                          <canvas
                            #chartCanvas
                            id="chat-canvas"
                            baseChart
                            [data]="chartData.data"
                            [options]="chartData.options"
                            [type]="'line'"
                            (chartClick)="onChartClick($event)"
                            (chartHover)="onChartHover($event)"
                            (mouseenter)="displayToolip = true"
                            (mouseleave)="displayToolip = false"
                            class="!w-full !h-[calc(100%-48px)] cursor-pointer"
                          >
                          </canvas>
                        </div>
                      </mat-grid-tile>
                    </mat-grid-list>
                  </div>
                </div>
              </ng-container>
            </mat-drawer-content>
          </mat-drawer-container>
        </ng-container>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </ng-container>

  <ng-container *ngrxLet="groupedTooltips$ as groupedTooltips">
    @if (displayToolip && !inZoom) {
      <div
        id="chartjs-tooltip-cursor-hor"
        class="fixed z-50 border-r-2 border-slate-400 cursor-pointer"
      ></div>
      <div
        id="chartjs-tooltip-cursor-vert"
        class="fixed z-50 border-t border-dashed border-slate-400 cursor-pointer"
      ></div>
      <div
        id="chartjs-tooltip"
        [ngClass]="{ 'opacity-0': !groupedTooltips?.length }"
        class="fixed z-50 bg-slate-400 bg-opacity-25 p-2 pb-0 rounded"
      >
        @if (commentMode) {
          <div class="font-bold mb-1">
            {{ "selectCommentPoint" | translate }}
          </div>
        }
        @for (group of groupedTooltips; track group) {
          <div class="mb-2">
            <div class="font-medium text-sm mb-1">{{ group.title }}</div>
            @for (tooltip of group.tooltips; track tooltip) {
              <div
                [style.background]="tooltip.color"
                class="flex justify-start items-center text-white text-xs font-medium mb-1 px-2.5 py-0.5 rounded opacity-80"
              >
                <div class="me-2">
                  {{ tooltip.variable | translate }}: {{ tooltip.y }}
                  {{ tooltip.uom }}
                </div>
              </div>
            }
          </div>
        }
        <ng-container *ngrxLet="expandedChartComments$ as chartComments">
          @for (comment of chartComments; track comment) {
            <div
              [style.background-color]="comment.color | alpha: 0.4"
              class="whitespace-pre-line text-sm px-2 py-1 mb-2 border border-graydark rounded max-w-90 max-h-[290px] overflow-hidden"
            >
              {{ comment.comment }}
            </div>
          }
        </ng-container>
      </div>
    }
  </ng-container>

  <ng-template #placeholderTmpl let-title="title">
    <div
      class="flex items-center justify-center bg-gray-200 w-full h-full text-5xl text-gray-400"
    >
      {{ title }}
    </div>
  </ng-template>

  <ng-template
    #recognitionToggleTmpl
    let-title="title"
    let-cameraId="cameraId"
    let-type="type"
  >
    <ng-container
      *ngrxLet="{ cameraId, recognitionType: type } | recSelected as selected"
    >
      <div class="ms-1 mt-4 mb-4">
        <mat-slide-toggle
          [checked]="selected"
          (click)="toggleRecognition(cameraId, type)"
          color="primary"
        >
          <span class="ps-2"> {{ title | translate }} </span>
        </mat-slide-toggle>
      </div>
    </ng-container>
  </ng-template>

  <ng-template #variablesListTmpl>
    <mat-chip-listbox multiple (click)="$event.stopPropagation()">
      <ng-container *ngrxLet="variables$ as variables">
        @for (variable of variables; track variable) {
          <ng-container *ngrxLet="variable | variableSelected as selected">
            <mat-chip-option
              [selected]="selected"
              [ngClass]="{ '!text-white !shadow-2': selected }"
              (click)="toggleVariable(variable)"
              class="!mt-0 !mb-2"
            >
              {{ variable.name | translate }}
            </mat-chip-option>
          </ng-container>
        }
      </ng-container>
      <ng-container *ngrxLet="recognitionVariables$ as recognitionVariables">
        @for (recVariable of recognitionVariables; track recVariable) {
          <ng-container
            *ngrxLet="recVariable | recVariableSelected as selected"
          >
            <mat-chip-option
              [selected]="selected"
              (click)="toggleRecognitionVariable(recVariable)"
              class="!mt-0 !mb-2"
            >
              {{ recVariable.name | translate }}
            </mat-chip-option>
          </ng-container>
        }
      </ng-container>
      <ng-container *ngrxLet="pointVariables$ as pointVariables">
        @for (pointVariable of pointVariables; track pointVariable) {
          <ng-container
            *ngrxLet="pointVariable | pointVariableSelected as selected"
          >
            <mat-chip-option
              [selected]="selected"
              (click)="togglePointVariable(pointVariable)"
              class="!mt-0 !mb-2"
            >
              {{ pointVariable.name | translate }}
            </mat-chip-option>
          </ng-container>
        }
      </ng-container>
    </mat-chip-listbox>
  </ng-template>

  <ng-template #dateRangePickerTmpl>
    <app-date-range-picker
      [dateRange$]="dateRange$"
      [showShiftControls]="true"
      (dateRangeChanged)="dateRangeChanged($event)"
      (shiftDateRange)="shiftDateRange($event)"
    ></app-date-range-picker>
  </ng-template>

  <ng-template #expandCameraViewTmpl>
    <div class="p-6">
      <button mat-button (click)="expandCameraView = !expandCameraView">
        @if (expandCameraView) {
          <ng-container>
            <mat-icon class="me-2">collapse_content</mat-icon>
            {{ "collapseCameraView" | translate }}
          </ng-container>
        } @else {
          <ng-container>
            <mat-icon class="me-2">open_in_full</mat-icon>
            {{ "expandCameraView" | translate }}
          </ng-container>
        }
      </button>
    </div>
  </ng-template>
</ng-container>
