<ng-container *ngrxLet="cameraImagesGrouped$ as cameraImagesGrouped">
  @for (group of cameraImagesGrouped; track group.cameraId) {
    <div class="flex flex-wrap -mx-3 px-8 py-7">
      @for (
        image of group.images;
        track image.camera.thermal_camera_id + image.viewType
      ) {
        <div class="px-3 mb-6">
          <app-camera-image
            [width]="400"
            [height]="303"
            [image]="image"
          ></app-camera-image>
        </div>
      }
    </div>
  }
</ng-container>
