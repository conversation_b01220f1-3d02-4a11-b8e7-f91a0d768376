import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable, combineLatest, filter, take } from 'rxjs';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { ICameraImage } from '../../../../../model/camera';
import { adminFeature } from '../../../../../state/admin/feature';
import { DashboardActions } from '../../state/dashboard.actions';
import { dashboardFeature } from '../../state/dashboard.feature';
import { CameraImageComponent } from '../camera-image/camera-image.component';

@Component({
  selector: 'app-cameras',
  standalone: true,
  imports: [...SharedModules, CommonModule, CameraImageComponent, LetDirective],
  templateUrl: './cameras.component.html',
  styleUrl: './cameras.component.scss',
})
export class CamerasComponent extends BaseComponent implements OnInit {
  cameraImagesGrouped$!: Observable<
    { cameraId: string; images: ICameraImage[] }[]
  >;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.cameraImagesGrouped$ = this.store.select(
      dashboardFeature.selectCameraImagesGrouped(true),
    );
    // TODO: think about refactoring that
    this.subSafe(
      combineLatest([
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
        this.store.select(adminFeature.selectLocationTimezone),
      ]).pipe(
        filter(([devices, tz]) => devices.length > 0 && !!tz),
        take(1),
      ),
      () =>
        this.store.dispatch(DashboardActions.refreshStandaloneCameraImages()),
    );
  }
}
