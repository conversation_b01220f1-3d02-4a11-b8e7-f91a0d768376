import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { ISaveCamPointPostRequest } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';

@Component({
  selector: 'app-camera-point-form',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './camera-point-dialog.component.html',
  styleUrl: './camera-point-dialog.component.scss',
})
export class CameraPointDialogComponent
  extends BaseComponent
  implements OnInit
{
  form = new FormGroup({
    name: new FormControl<string | undefined>(undefined, {
      nonNullable: true,
    }),
    last_x_position: new FormControl<number>(0, {
      nonNullable: true,
      validators: [Validators.required],
    }),
    last_y_position: new FormControl<number>(0, {
      nonNullable: true,
      validators: [Validators.required],
    }),
    color: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
  });

  constructor(
    private dialogRef: MatDialogRef<CameraPointDialogComponent>,
    @Inject(MAT_DIALOG_DATA) private point: ISaveCamPointPostRequest,
  ) {
    super();
  }

  ngOnInit() {
    this.form.reset(this.point);
  }

  confirm() {
    this.dialogRef.close(
      this.form.getRawValue() satisfies ISaveCamPointPostRequest,
    );
  }
}
