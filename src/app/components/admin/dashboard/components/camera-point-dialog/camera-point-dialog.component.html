<button mat-icon-button mat-dialog-close class="!absolute right-2 top-2 !z-10">
  <mat-icon>close</mat-icon>
</button>
<h2 mat-dialog-title>{{ "pointConfiguration" | translate }}</h2>
<div mat-dialog-content class="w-80 !pb-0">
  <div [formGroup]="form" class="flex flex-col">
    <mat-form-field>
      <mat-label>{{ "pointName" | translate }}</mat-label>
      <input formControlName="name" matInput />
    </mat-form-field>

    <mat-form-field>
      <mat-label>{{ "xCoordinate" | translate }}</mat-label>
      <input formControlName="last_x_position" matInput type="number" />
    </mat-form-field>

    <mat-form-field>
      <mat-label>{{ "yCoordinate" | translate }}</mat-label>
      <input formControlName="last_y_position" matInput type="number" />
    </mat-form-field>

    <input
      type="color"
      formControlName="color"
      class="w-full h-12 mb-6 cursor-pointer"
    />
  </div>
</div>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-flat-button [mat-dialog-close]="false">
    {{ "cancel" | translate }}
  </button>
  <div>
    <button mat-flat-button color="primary" (click)="confirm()">
      {{ "confirm" | translate }}
    </button>
  </div>
</mat-dialog-actions>
