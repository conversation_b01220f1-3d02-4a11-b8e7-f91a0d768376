<h2 mat-dialog-title>{{ "yAxisConfig" | translate }}</h2>
<div mat-dialog-content class="w-full lg:w-80 !pb-0">
  <div [formGroup]="form" class="flex flex-col">
    <mat-form-field>
      <mat-label>{{ "maxValue" | translate }}</mat-label>
      <input formControlName="maxValue" matInput type="number" />
      <button
        mat-icon-button
        matSuffix
        (click)="form.controls.maxValue.setValue(undefined)"
      >
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>

    <mat-form-field>
      <mat-label>{{ "minValue" | translate }}</mat-label>
      <input formControlName="minValue" matInput type="number" />
      <button
        mat-icon-button
        matSuffix
        (click)="form.controls.minValue.setValue(undefined)"
      >
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>
  </div>
</div>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-flat-button [mat-dialog-close]="false">
    {{ "cancel" | translate }}
  </button>
  <div>
    <button mat-flat-button (click)="clear()" class="me-1">
      {{ "clear" | translate }}
    </button>
    <button mat-flat-button color="primary" (click)="confirm()">
      {{ "confirm" | translate }}
    </button>
  </div>
</mat-dialog-actions>
