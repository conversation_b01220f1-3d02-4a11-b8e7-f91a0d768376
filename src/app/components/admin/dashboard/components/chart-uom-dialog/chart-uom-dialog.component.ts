import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { BaseComponent } from '../../../../../common/base.component';
import { IChartUomConfig } from '../../../../../model/dashboard';

@Component({
  selector: 'app-chart-uom-form',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './chart-uom-dialog.component.html',
  styleUrl: './chart-uom-dialog.component.scss',
})
export class ChartUomDialogComponent extends BaseComponent implements OnInit {
  form = new FormGroup({
    minValue: new FormControl<number | undefined>(undefined),
    maxValue: new FormControl<number | undefined>(undefined),
  });

  constructor(
    private dialogRef: MatDialogRef<ChartUomDialogComponent>,
    @Inject(MAT_DIALOG_DATA) private uomConfig: IChartUomConfig,
  ) {
    super();
  }

  ngOnInit() {
    this.form.reset(this.uomConfig);
  }

  confirm() {
    this.dialogRef.close(this.form.value);
  }

  clear() {
    this.dialogRef.close({
      minValue: undefined,
      maxValue: undefined,
    });
  }
}
