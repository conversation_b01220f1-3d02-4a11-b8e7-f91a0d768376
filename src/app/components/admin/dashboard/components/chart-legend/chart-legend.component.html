@if (chartData.metadata) {
  <div class="flex flex-wrap -mx-1.5">
    @for (legendItem of chartData.metadata.legend; track legendItem.uom) {
      <ng-container *ngrxLet="legendItem.uom | uomConfig as uomConfig">
        <div class="px-1.5 mb-3">
          <div
            matBadge="*"
            [matBadgeHidden]="!uomConfig"
            matBadgeColor="accent"
            class="ps-4 pe-2 pb-1 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark flex items-center justify-center"
          >
            <div class="flex flex-col lg:flex-row">
              <div
                class="flex items-center text-base font-semibold pt-2 lg:pt-0 me-2 mt-2 mb-1"
              >
                {{ legendItem.uom }}
              </div>
              <div class="flex flex-wrap items-start justify-start">
                @for (
                  datasetGroup of legendItem.datasetGroups;
                  track datasetGroup.variable
                ) {
                  <div
                    class="flex flex-wrap lg:ms-2 mt-1 max-h-22 overflow-auto"
                  >
                    <div class="me-3 my-1 font-medium">
                      {{ datasetGroup.variable | translate }}
                    </div>
                    @for (
                      dataset of datasetGroup.datasets;
                      track dataset.name
                    ) {
                      <div
                        [matTooltip]="!dataset.isActive ? 'No data' : ''"
                        [style.background-color]="dataset.color"
                        [ngClass]="{
                          'border-none': !dataset.borderDash?.[0],
                          'border-dotted': dataset.borderDash?.[0] === 1,
                          'border-dashed': dataset.borderDash?.[0] === 7,
                          'opacity-30': !dataset.isActive
                        }"
                        class="h-6 text-white text-xs font-medium me-2 my-1 px-2.5 py-0.5 rounded flex items-center justify-center"
                      >
                        {{ dataset.name }}
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
            @if (!readOnly) {
              <div
                class="ps-2 w-10 h-full flex-none flex items-center justify-center rounded-tr-lg rounded-br-lg cursor-pointer bg-gray-200"
              >
                <button
                  (click)="
                    onOpenLegendItemDetails.emit({ legendItem, uomConfig })
                  "
                  class="w-6 h-6 mt-1"
                >
                  <mat-icon class="text-slate-400"> edit </mat-icon>
                </button>
              </div>
            }
          </div>
        </div>
      </ng-container>
    }
  </div>
}
