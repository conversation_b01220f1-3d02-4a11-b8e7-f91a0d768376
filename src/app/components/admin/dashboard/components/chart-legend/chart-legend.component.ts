import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { LetDirective } from '@ngrx/component';
import { TranslateModule } from '@ngx-translate/core';
import { IChartData, IChartLegendItem } from '../../../../../model/dashboard';
import { UomConfigPipe } from '../../pipes/uom-config.pipe';

@Component({
  selector: 'app-chart-legend',
  standalone: true,
  imports: [
    CommonModule,
    MatBadgeModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule,
    LetDirective,
    UomConfigPipe,
  ],
  templateUrl: './chart-legend.component.html',
  styleUrls: [],
})
export class ChartLegendComponent {
  @Input() chartData!: IChartData;
  @Input() readOnly = false;

  @Output() onOpenLegendItemDetails = new EventEmitter<{
    legendItem: IChartLegendItem;
    uomConfig: any;
  }>();
}
