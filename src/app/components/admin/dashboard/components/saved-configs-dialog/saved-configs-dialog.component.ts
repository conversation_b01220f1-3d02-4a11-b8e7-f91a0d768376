import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, tap } from 'rxjs';
import { IChartsFavouriteResponseItem } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { DashboardActions } from '../../state/dashboard.actions';
import { dashboardFeature } from '../../state/dashboard.feature';

@Component({
  selector: 'app-saved-configs',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    LetDirective,
  ],
  templateUrl: './saved-configs-dialog.component.html',
  styleUrl: './saved-configs-dialog.component.scss',
})
export class SavedConfigsDialogComponent
  extends BaseComponent
  implements OnInit
{
  chartFavourites$!: Observable<IChartsFavouriteResponseItem[]>;
  selectedChartFavouriteId$!: Observable<number | null>;

  nameCtrl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  private chartFavouritesCache: IChartsFavouriteResponseItem[] = [];

  constructor(
    private store: Store,
    private dialogRef: MatDialogRef<SavedConfigsDialogComponent>,
  ) {
    super();
  }

  ngOnInit() {
    this.chartFavourites$ = this.store
      .select(dashboardFeature.selectChartFavouritesForActiveLocation)
      .pipe(
        tap((chartFavourites) => (this.chartFavouritesCache = chartFavourites)),
      );
    this.selectedChartFavouriteId$ = this.store.select(
      dashboardFeature.selectSelectedChartFavouriteId,
    );
  }

  save() {
    this.store.dispatch(
      DashboardActions.createChartFavourite({
        name: this.nameCtrl.value,
      }),
    );

    this.nameCtrl.reset('');
  }

  apply(chartFavourite: IChartsFavouriteResponseItem) {
    this.store.dispatch(
      DashboardActions.applyChartFavourite({ id: chartFavourite.id }),
    );
    this.dialogRef.close(true);
  }

  setAsDefault(nextDefault: IChartsFavouriteResponseItem) {
    this.tryUnsetCurrentDefault();
    this.store.dispatch(
      DashboardActions.toggleChartFavouriteDefault({
        id: nextDefault.id,
        isDefault: true,
      }),
    );
  }

  unsetAsDefault() {
    this.tryUnsetCurrentDefault();
  }

  private tryUnsetCurrentDefault() {
    const currentDefault = this.chartFavouritesCache.find(
      (cf) => cf.is_default,
    );

    if (!currentDefault) {
      return;
    }

    this.store.dispatch(
      DashboardActions.toggleChartFavouriteDefault({
        id: currentDefault.id,
        isDefault: false,
      }),
    );
  }

  update(chartFavourite: IChartsFavouriteResponseItem) {
    this.store.dispatch(
      DashboardActions.updateChartFavourite({ id: chartFavourite.id }),
    );
    this.dialogRef.close();
  }

  async delete(chartFavourite: IChartsFavouriteResponseItem) {
    if (!(await this.ui.confirm())) {
      return;
    }
    this.store.dispatch(
      DashboardActions.deleteChartFavourite({ id: chartFavourite.id }),
    );
  }
}
