<button mat-icon-button mat-dialog-close class="!absolute right-2 top-2 !z-10">
  <mat-icon>close</mat-icon>
</button>
<h2 mat-dialog-title>{{ "dashboardConfigurations" | translate }}</h2>
<div mat-dialog-content class="w-full lg:w-[400px] !pb-0">
  <ng-container *ngrxLet="chartFavourites$ as chartFavourites">
    @if (chartFavourites.length) {
      <div class="mb-4">
        <h3 class="!mb-0">{{ "savedDashboards" | translate }}</h3>
        <mat-list role="list">
          <ng-container
            *ngrxLet="selectedChartFavouriteId$ as selectedChartFavouriteId"
          >
            @for (chartFavourite of chartFavourites; track chartFavourite) {
              <mat-list-item
                role="listitem"
                [ngClass]="{
                  '!bg-slate-200 !rounded-lg':
                    chartFavourite.id === selectedChartFavouriteId
                }"
                class="!ps-2 !pe-0"
              >
                <mat-icon
                  matListItemIcon
                  [ngClass]="{ filled: chartFavourite.is_default }"
                  class="!ms-0 !me-3"
                >
                  dashboard
                </mat-icon>
                <div
                  matListItemTitle
                  class="w-full flex items-center justify-start"
                >
                  <div class="overflow-hidden overflow-ellipsis">
                    <div>
                      {{ chartFavourite.name }}
                    </div>
                    @if (chartFavourite.is_default) {
                      <span class="text-sm text-slate-600">
                        {{ "default" | translate }}
                      </span>
                    }
                  </div>
                  <span class="flex-1"></span>
                  <button mat-icon-button (click)="apply(chartFavourite)">
                    <mat-icon class="text-gray-500">check</mat-icon>
                  </button>
                  <button mat-icon-button [matMenuTriggerFor]="menu">
                    <mat-icon class="text-gray-500">more_vert</mat-icon>
                  </button>
                  <mat-menu #menu="matMenu">
                    @if (chartFavourite.is_default) {
                      <button mat-menu-item (click)="unsetAsDefault()">
                        <mat-icon class="!text-gray-500">remove_done</mat-icon>
                        <span>{{ "unsetAsDefault" | translate }}</span>
                      </button>
                    } @else {
                      <button
                        mat-menu-item
                        (click)="setAsDefault(chartFavourite)"
                      >
                        <mat-icon class="!text-gray-500">done_all</mat-icon>
                        <span>{{ "setAsDefault" | translate }}</span>
                      </button>
                    }
                    <button mat-menu-item (click)="update(chartFavourite)">
                      <mat-icon class="!text-gray-500">save</mat-icon>
                      <span>{{ "update" | translate }}</span>
                    </button>
                    <button mat-menu-item (click)="delete(chartFavourite)">
                      <mat-icon class="!text-gray-500">delete</mat-icon>
                      <span>{{ "delete" | translate }}</span>
                    </button>
                  </mat-menu>
                </div>
              </mat-list-item>
            }
          </ng-container>
        </mat-list>
        <mat-divider></mat-divider>
      </div>
    }
  </ng-container>

  <div class="!mb-1">
    <h3 class="mb-4">{{ "saveCurrentDashboard" | translate }}</h3>
    <mat-form-field class="w-full">
      <mat-label>{{ "name" | translate }}</mat-label>
      <input matInput [formControl]="nameCtrl" />
      <button
        matSuffix
        mat-icon-button
        [disabled]="!nameCtrl.valid"
        (click)="save()"
      >
        <mat-icon>save</mat-icon>
      </button>
    </mat-form-field>
  </div>
</div>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-flat-button cdkFocusInitial [mat-dialog-close]="false">
    {{ "close" | translate }}
  </button>
</mat-dialog-actions>
