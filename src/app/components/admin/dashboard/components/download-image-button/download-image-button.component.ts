import { Component, Input } from '@angular/core';
import { saveAs } from 'file-saver';
import SharedModules from '../../../../../common/shared.modules';

@Component({
  selector: 'app-download-image-button',
  standalone: true,
  imports: [...SharedModules],
  templateUrl: './download-image-button.component.html',
  styleUrls: [],
})
export class DownloadImageButtonComponent {
  @Input() canvas!: HTMLCanvasElement;
  @Input() imageName!: string;

  downloadImage() {
    this.canvas.toBlob((blob: Blob | null) => {
      if (!blob) {
        return;
      }
      saveAs(blob, `${this.imageName}.png`);
    });
  }
}
