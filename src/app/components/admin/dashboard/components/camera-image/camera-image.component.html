<div class="flex justify-center">
  <div class="flex-1">
    <div class="text-base">
      {{ "camera" | translate }} {{ cameraImage.camera.thermal_camera_id }}
    </div>
    <div class="mb-1 text-sm">{{ description }}</div>
  </div>
  @if (cameraImage.id >= 0) {
    <button
      mat-icon-button
      (click)="openCameraDetails()"
      class="flex-none w-10 h-10"
    >
      <mat-icon class="text-slate-400">edit</mat-icon>
    </button>
  }
</div>

<div class="flex flex-row">
  <div class="relative flex-none w-2 mr-1">
    <div class="absolute top-1 left-1 z-10 bg-white rounded px-1 text-xs">
      {{ cameraImage.max }}
    </div>
    <div class="absolute bottom-1 left-1 z-10 bg-white rounded px-1 text-xs">
      {{ cameraImage.min }}
    </div>
    @switch (cameraImage.viewType) {
      @case (viewTypes.TemperaturePlus) {
        <img
          src="assets/camera/jet-palette-90.png"
          [style.height.px]="height"
          alt="jet-palette"
          class="w-full"
        />
      }
      @case (viewTypes.StomataPlus) {
        <img
          src="assets/camera/viridis.png"
          [style.height.px]="height"
          alt="viridis"
          class="w-full"
        />
      }
    }
  </div>
  <div class="grow relative">
    <canvas
      #canvas
      [width]="width"
      [height]="height"
      (click)="onCanvasClick($event)"
    ></canvas>
    @if (drawn) {
      <app-download-image-button
        [canvas]="canvas"
        [imageName]="
          cameraImage.camera.thermal_camera_id +
          '-' +
          cameraImage.camera.remote_id
        "
      ></app-download-image-button>
    }
    @if (cameraImage.id < 0) {
      <div
        class="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-slate-200 rounded-lg"
      >
        <div class="text-xl text-slate-500">Camera offline</div>
      </div>
    }
  </div>
</div>
