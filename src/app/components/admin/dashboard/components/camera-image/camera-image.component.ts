import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import {
  GetCamSeriePointPosition,
  IGetCurrentCamPoint,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { CameraViewType, ICameraImage } from '../../../../../model/camera';
import { ICustomPoint } from '../../../../../model/dashboard';
import { adminFeature } from '../../../../../state/admin/feature';
import { CanvasUtils } from '../../../../../utils/canvas';
import { DashboardActions } from '../../state/dashboard.actions';
import { CameraDetailsDialogComponent } from '../camera-details-dialog/camera-details-dialog.component';
import { DownloadImageButtonComponent } from '../download-image-button/download-image-button.component';

@Component({
  selector: 'app-camera-image',
  standalone: true,
  imports: [...SharedModules, CommonModule, DownloadImageButtonComponent],
  templateUrl: './camera-image.component.html',
  styleUrl: './camera-image.component.scss',
})
export class CameraImageComponent extends BaseComponent implements OnInit {
  @ViewChild('canvas') canvas!: ElementRef;

  @Input() width = 243;
  @Input() height = 184;

  @Input() set image(value: ICameraImage) {
    this.cameraImage = value;
    this.description = `${value.viewType[0].toUpperCase()}${value.viewType.substring(
      1,
    )}, ${dayjs(value.timestamp, 'YYYYMMDDHHmmss').format('ddd HH:mm:ss')}`;
    this.pointPosition = value.pointPosition;

    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      this.img = img;
      this.draw();
    };
    img.src = value.imageUrl;
  }

  viewTypes = CameraViewType;

  cameraImage!: ICameraImage;
  description!: string;
  drawn = false;

  private xScale = 0;
  private yScale = 0;
  private points: IGetCurrentCamPoint[] = [];
  private pointPosition?: {
    [key: string]: GetCamSeriePointPosition;
  };
  private img?: HTMLImageElement;

  constructor(
    private store: Store,
    private dialog: MatDialog,
  ) {
    super();
  }

  ngOnInit() {
    this.xScale = this.width / 1600;
    this.yScale = this.height / 1200;
    this.subSafe(
      this.store.select(
        adminFeature.selectDevicePointsById(
          this.cameraImage.camera.thermal_camera_id,
        ),
      ),
      (points) => {
        this.points = points;
        this.draw();
      },
    );
  }

  onCanvasClick(event: MouseEvent) {
    const { serverX, serverY } = CanvasUtils.getServerCoordinatesFromMouseEvent(
      event,
      this.width,
      this.height,
    );

    const customPoint: ICustomPoint = {
      cameraId: this.cameraImage.camera.thermal_camera_id,
      clientX: event.offsetX,
      clientY: event.offsetY,
      serverX,
      serverY,
    };
    this.store.dispatch(DashboardActions.toggleCustomPoint({ customPoint }));
  }

  openCameraDetails() {
    this.dialog.open(CameraDetailsDialogComponent, {
      data: this.cameraImage,
    });
  }

  private draw() {
    if (!this.img) {
      return;
    }

    requestAnimationFrame(() => {
      const ctx = this.canvas.nativeElement.getContext('2d');

      if (!ctx) {
        return;
      }

      ctx.clearRect(0, 0, this.width, this.height);

      ctx.drawImage(this.img!, 0, 0, this.width, this.height);

      const recognisedClasses = this.cameraImage.recognitionConfig?.map((rc) =>
        this.cameraImage.recognitionData
          ? this.cameraImage.recognitionData[rc.recognitionType]?.objects
          : [],
      );

      ctx.strokeStyle = 'red';
      ctx.lineWidth = 1.3;

      for (const recognisedClass of recognisedClasses ?? []) {
        for (const detectedObject of recognisedClass ?? []) {
          const area = detectedObject.area;
          ctx.beginPath();
          ctx.moveTo(this.getClientX(area[0][0]), this.getClientY(area[0][1]));
          for (let i = 1; i < area.length; i++) {
            ctx.lineTo(
              this.getClientX(area[i][0]),
              this.getClientY(area[i][1]),
            );
          }
          ctx.closePath();
          ctx.stroke();
        }
      }

      ctx.strokeStyle = 'yellow';
      ctx.lineWidth = 2;

      for (const point of this.points) {
        const position = this.pointPosition?.[point.id];
        if (!position) {
          continue;
        }

        ctx.beginPath();
        ctx.arc(
          position.x * (this.width / CanvasUtils.serverImageWidth),
          position.y * (this.height / CanvasUtils.serverImageHeight),
          3,
          0,
          2 * Math.PI,
        );
        ctx.fillStyle = point.color;
        ctx.fill();
      }

      this.drawn ||= true;
    });
  }

  private getClientX(serverX: number) {
    return this.xScale * serverX;
  }

  private getClientY(serverY: number) {
    return this.yScale * serverY;
  }
}
