/* eslint-disable @typescript-eslint/no-explicit-any */

import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import {
  Component,
  ElementRef,
  Inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import {
  Observable,
  Subject,
  combineLatest,
  filter,
  lastValueFrom,
  take,
} from 'rxjs';
import {
  CameraApi,
  IGetCurrentCamPoint,
  ISaveCamPointPostRequest,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { PointNamePipe } from '../../../../../common/pipes/point-name.pipe';
import SharedModules from '../../../../../common/shared.modules';
import { CameraViewType, ICameraImage } from '../../../../../model/camera';
import { adminFeature } from '../../../../../state/admin/feature';
import { CanvasUtils } from '../../../../../utils/canvas';
import { CameraPointDialogComponent } from '../camera-point-dialog/camera-point-dialog.component';
import { DownloadImageButtonComponent } from '../download-image-button/download-image-button.component';
import { AdminActions } from './../../../../../state/admin/actions';

@Component({
  selector: 'app-camera-details',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    OverlayModule,
    DownloadImageButtonComponent,
    LetDirective,
    PointNamePipe,
  ],
  templateUrl: './camera-details-dialog.component.html',
  styleUrl: './camera-details-dialog.component.scss',
})
export class CameraDetailsDialogComponent
  extends BaseComponent
  implements OnInit
{
  @ViewChild('canvas') canvas!: ElementRef;

  points$!: Observable<IGetCurrentCamPoint[]>;

  height = Math.round(window.innerHeight * 0.6);
  width = Math.round(this.height * CanvasUtils.serverAspectRatio);

  serverAspectRatio = CanvasUtils.serverAspectRatio;
  pointPendingCoordinated?: IGetCurrentCamPoint;
  fullscreen = false;

  private image$ = new Subject<HTMLImageElement>();
  private readings: number[][] = [];
  private tooltipEl: HTMLElement | undefined;

  constructor(
    @Inject(MAT_DIALOG_DATA) public cameraImage: ICameraImage,
    private store: Store,
    private dialog: MatDialog,
    private http: HttpClient,
    private cameraApi: CameraApi,
  ) {
    super();
  }

  ngOnInit() {
    this.points$ = this.store.select(
      adminFeature.selectDevicePointsById(
        this.cameraImage.camera.thermal_camera_id,
      ),
    );

    this.subSafe(
      combineLatest([this.image$, this.points$]).pipe(
        filter(([image, points]) => !!image && !!points.length),
      ),
      (params: any[]) => this.draw(params[0], params[1]),
    );

    this.tooltipEl = document.getElementById('reading-tooltip') ?? undefined;

    this.loadImageData();
  }

  async onCanvasClick(event: MouseEvent) {
    if (this.fullscreen) {
      return;
    }

    const { serverX: last_x_position, serverY: last_y_position } =
      CanvasUtils.getServerCoordinatesFromMouseEvent(
        event,
        this.width,
        this.height,
      );

    if (this.pointPendingCoordinated) {
      this.store.dispatch(
        AdminActions.updateDevicePoint({
          cameraId: this.cameraImage.camera.thermal_camera_id,
          point: {
            ...this.pointPendingCoordinated,
            last_x_position,
            last_y_position,
          },
        }),
      );
      this.pointPendingCoordinated = undefined;
    } else {
      const pointsCount = (await lastValueFrom(this.points$.pipe(take(1))))
        .length;

      if (pointsCount >= 9) {
        this.ui.showMessage('Maximum number of points reached');
        return;
      }

      const newPoint = await this.openCameraPointForm({
        name: undefined,
        last_x_position,
        last_y_position,
        color: '#333333',
      });

      if (!newPoint) {
        return;
      }

      this.store.dispatch(
        AdminActions.addDevicePoint({
          cameraId: this.cameraImage.camera.thermal_camera_id,
          point: newPoint,
        }),
      );
    }
  }

  onCanvasPointerMove(event: PointerEvent) {
    if (!this.tooltipEl) {
      return;
    }

    const { serverX, serverY } = CanvasUtils.getServerCoordinatesFromMouseEvent(
      event,
      this.width,
      this.height,
    );

    this.tooltipEl.style.opacity = '1';
    this.tooltipEl.style.position = 'absolute';
    this.tooltipEl.style.left = event.offsetX + 20 + 'px';
    this.tooltipEl.style.top = event.offsetY + 54 + 'px';
    this.tooltipEl.style.pointerEvents = 'none';
    this.tooltipEl.innerHTML = this.readings[serverY]?.[serverX]?.toString();
  }

  onCanvasPointerLeave() {
    if (!this.tooltipEl) {
      return;
    }

    this.tooltipEl.style.opacity = '0';
  }

  async editDevicePoint(point: IGetCurrentCamPoint) {
    const updatedPoint = await this.openCameraPointForm(point);

    if (!updatedPoint) {
      return;
    }

    this.store.dispatch(
      AdminActions.updateDevicePoint({
        cameraId: this.cameraImage.camera.thermal_camera_id,
        point: {
          ...updatedPoint,
          id: point.id,
        },
      }),
    );
  }

  editDevicePointCoordinates(point: IGetCurrentCamPoint) {
    this.pointPendingCoordinated = point;
  }

  cancelDevicePointCoordinatesEdit() {
    this.pointPendingCoordinated = undefined;
  }

  private draw(
    image: HTMLImageElement,
    currentCamPoints: IGetCurrentCamPoint[],
  ) {
    requestAnimationFrame(async () => {
      const ctx = this.canvas.nativeElement.getContext('2d');

      ctx.clearRect(0, 0, this.width, this.height);

      ctx.drawImage(image, 0, 0, this.width, this.height);

      for (const currentCamPoint of currentCamPoints) {
        ctx.beginPath();
        ctx.arc(
          currentCamPoint.last_x_position *
            (this.width / CanvasUtils.serverImageWidth),
          currentCamPoint.last_y_position *
            (this.height / CanvasUtils.serverImageHeight),
          5,
          0,
          2 * Math.PI,
        );
        ctx.fillStyle = currentCamPoint.color;
        ctx.fill();
      }
    });
  }

  private async openCameraPointForm(point: ISaveCamPointPostRequest) {
    const dialogRef = this.dialog.open(CameraPointDialogComponent, {
      data: point,
    });
    const res: ISaveCamPointPostRequest | undefined = await lastValueFrom(
      dialogRef.afterClosed(),
    );
    return res;
  }

  private async loadImageData() {
    await this.execSafe(async () => {
      const imageData = (
        await lastValueFrom(
          this.cameraApi.getLatestHqImagesRetrieve(
            this.cameraImage.camera.thermal_camera_id,
          ),
        )
      ).data;

      const imageUrl =
        this.cameraImage.viewType === CameraViewType.StomataPlus
          ? imageData.stomata
          : imageData.temperature;

      if (!imageUrl) {
        return;
      }

      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => this.image$.next(img);
      img.src = imageUrl;

      this.readings = (await lastValueFrom(
        this.http.get(this.cameraImage.imageUrl.replace('.png', '.json')),
      )) as any;
    });
  }
}
