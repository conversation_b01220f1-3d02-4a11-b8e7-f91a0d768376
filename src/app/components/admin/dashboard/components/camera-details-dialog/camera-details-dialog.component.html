<button
  mat-icon-button
  (click)="fullscreen = !fullscreen"
  class="!absolute right-16 top-2 !z-10"
>
  <mat-icon>fullscreen</mat-icon>
</button>
<button mat-icon-button mat-dialog-close class="!absolute right-2 top-2 !z-10">
  <mat-icon>close</mat-icon>
</button>
<div class="text-center text-black border-b border-stroke !py-1.5 !mb-0">
  <div class="text-lg font-bold">
    {{ "camera" | translate }} {{ cameraImage.camera.thermal_camera_id }}
  </div>
  <div class="text-base">{{ "addNewPointHint" | translate }}</div>
</div>
<mat-dialog-content
  [ngClass]="{ 'full-screen': fullscreen }"
  class="!p-4 !pb-0 mat-typography !overflow-hidden"
>
  @if (fullscreen) {
    <button
      mat-icon-button
      (click)="fullscreen = false"
      class="exit-full-screen-btn"
    >
      <mat-icon>close</mat-icon>
    </button>
  }
  <div class="flex">
    <div class="relative flex-none">
      <canvas
        #canvas
        [width]="width"
        [height]="height"
        (pointermove)="onCanvasPointerMove($event)"
        (pointerleave)="onCanvasPointerLeave()"
        (click)="onCanvasClick($event)"
        class="cursor-crosshair rounded-md"
      ></canvas>
      <app-download-image-button
        [canvas]="canvas"
        [imageName]="
          cameraImage.camera.thermal_camera_id +
          '-' +
          cameraImage.camera.remote_id
        "
      ></app-download-image-button>
    </div>
    <div
      *ngrxLet="points$ as points"
      [style.height.px]="height"
      class="flex-none w-60 px-2 overflow-auto overflow-x-hidden relative"
    >
      @if (pointPendingCoordinated) {
        <div
          (click)="cancelDevicePointCoordinatesEdit()"
          class="absolute w-full h-full bg-white opacity-70 z-10"
        ></div>
      }
      @for (point of points; track point.id) {
        <div class="flex items-center mt-1 mb-1 w-full">
          <div class="flex-1 mx-2 text-sm text-black">
            {{ point | pointName | async }}
          </div>
          <div
            [style.background]="point.color"
            class="flex-none w-4 h-4 rounded me-3"
          ></div>
          <button
            mat-icon-button
            (click)="editDevicePoint(point)"
            class="!p-2 !w-10 !h-10"
          >
            <mat-icon class="text-gray-500">edit</mat-icon>
          </button>
          <button
            mat-icon-button
            (click)="editDevicePointCoordinates(point)"
            class="!p-2 !w-10 !h-10"
          >
            <mat-icon class="text-gray-500">my_location</mat-icon>
          </button>
        </div>
        <mat-divider></mat-divider>
      }
    </div>

    <div
      id="reading-tooltip"
      style="opacity: 0"
      class="z-50 bg-black bg-opacity-50 text-white px-1 rounded fixed"
    ></div>
  </div>
</mat-dialog-content>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-button mat-dialog-close cdkFocusInitial>
    {{ "close" | translate }}
  </button>
</mat-dialog-actions>
