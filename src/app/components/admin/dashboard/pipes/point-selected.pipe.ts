import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IGetCurrentCamPoint } from '../../../../api/api-sdk';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'pointSelected',
  standalone: true,
})
export class PointSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(point: IGetCurrentCamPoint) {
    return this.store.select(dashboardFeature.isPointSelected(point));
  }
}
