import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { ICameraVariable } from '../../../../model/dashboard';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'pointVariableSelected',
  standalone: true,
})
export class PointVariableSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(variable: ICameraVariable) {
    return this.store.select(dashboardFeature.isPointVarSelected(variable));
  }
}
