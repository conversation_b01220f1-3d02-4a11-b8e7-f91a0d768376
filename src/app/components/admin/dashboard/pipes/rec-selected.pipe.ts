import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { ICameraRecognitionConfig } from '../../../../model/dashboard';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'recSelected',
  standalone: true,
})
export class RecognitionSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(recognition: ICameraRecognitionConfig) {
    return this.store.select(dashboardFeature.isRecSelected(recognition));
  }
}
