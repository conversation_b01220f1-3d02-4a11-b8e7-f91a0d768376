import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IDevice } from '../../../../model/dashboard';
import { adminFeature } from '../../../../state/admin/feature';

@Pipe({
  name: 'deviceSettings',
  standalone: true,
})
export class DeviceSettingsPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(device: IDevice) {
    return device.thermal_camera_id
      ? this.store.select(
          adminFeature.selectDeviceSettings(device.thermal_camera_id),
        )
      : undefined;
  }
}
