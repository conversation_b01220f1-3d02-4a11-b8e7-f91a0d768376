import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IDevice } from '../../../../model/dashboard';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'deviceCustomPoints',
  standalone: true,
})
export class DeviceCustomPointsPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(device: IDevice) {
    return device.thermal_camera_id
      ? this.store.select(
          dashboardFeature.selectDeviceCustomPoints(device.thermal_camera_id),
        )
      : undefined;
  }
}
