import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IVariableToDisplay } from '../../../../api/api-sdk';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'variableSelected',
  standalone: true,
})
export class VariableSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(variable: IVariableToDisplay) {
    return this.store.select(dashboardFeature.isVarSelected(variable));
  }
}
