import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IDevice } from '../../../../model/dashboard';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'deviceSelected',
  standalone: true,
})
export class DeviceSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(device: IDevice) {
    return this.store.select(dashboardFeature.isDeviceSelected(device));
  }
}
