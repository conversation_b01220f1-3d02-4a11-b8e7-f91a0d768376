import { Pipe, PipeTransform } from '@angular/core';
import { IThermalCamSettings } from '../../../../api/api-sdk';
import { RecognitionType } from '../../../../model/dashboard';

@Pipe({
  name: 'recTypeSupported',
  standalone: true,
})
export class RecognitionTypeSupportedPipe implements PipeTransform {
  transform(
    settings: IThermalCamSettings | undefined,
    recType: RecognitionType,
  ): unknown {
    return settings?.supported_recognition_types?.includes(recType.toString());
  }
}
