import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { IDevice } from '../../../../model/dashboard';
import { adminFeature } from '../../../../state/admin/feature';

@Pipe({
  name: 'devicePoints',
  standalone: true,
})
export class DevicePointsPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(device: IDevice) {
    return device.thermal_camera_id
      ? this.store.select(
          adminFeature.selectDevicePointsById(device.thermal_camera_id),
        )
      : undefined;
  }
}
