import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { ICameraVariable } from '../../../../model/dashboard';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'recVariableSelected',
  standalone: true,
})
export class RecognitionVariableSelectedPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(variable: ICameraVariable) {
    return this.store.select(dashboardFeature.isRecVarSelected(variable));
  }
}
