import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { dashboardFeature } from '../state/dashboard.feature';

@Pipe({
  name: 'uomConfig',
  standalone: true,
})
export class UomConfigPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(uom: string) {
    return this.store.select(dashboardFeature.selectChartUomConfigByName(uom));
  }
}
