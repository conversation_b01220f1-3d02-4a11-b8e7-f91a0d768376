/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  debounce,
  delay,
  exhaustMap,
  filter,
  forkJoin,
  from,
  interval,
  map,
  switchMap,
  tap,
} from 'rxjs';
import {
  ChartSnapshotRequest,
  ChartsCommentPostRequest,
  ChartsFavouritePostRequest,
  DataApi,
  IGetCurrentCamPoint,
} from '../../../../api/api-sdk';
import {
  DashboardCacheType,
  IChartCommentConfig,
  IConfiguredDevice,
  ILatestChartState,
} from '../../../../model/dashboard';
import { DateRangesPredefined } from '../../../../model/dateRange';
import { AdminActions } from '../../../../state/admin/actions';
import { adminFeature } from '../../../../state/admin/feature';
import { DateUtils } from '../../../../utils/date';
import { NodesActions } from '../../nodes/state/nodes.actions';
import { DashboardService } from '../services/dashboard.service';
import { dateRanges } from './../../../../model/dateRange';
import { AuthActions } from './../../../../state/auth/actions';
import { DashboardUtils } from './../../../../utils/dashboard';
import { DashboardActions } from './dashboard.actions';
import { dashboardFeature } from './dashboard.feature';

@Injectable()
export class DashboardEffects {
  private readonly savedConfigsPersistentKey = 'sigrow:savedConfigs';
  private readonly savedConfigsPersistentKeyBU = 'sigrow:savedConfigsBU';
  private readonly latestChartStatePersistentKey = 'sigrow:latestChartState';

  readings$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        DashboardActions.dateRangeChanged,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(dashboardFeature.selectDateRange),
      ]),
      filter((values) => values.every((v) => !!v)),
      switchMap(([, activeLocation, dateRange]) =>
        // TODO: change that to ensureReadingsCache
        this.dashboardMng.loadReadings(activeLocation!, dateRange).pipe(
          map((res) =>
            DashboardActions.cacheUpdated({
              key: DashboardCacheType.readings,
              value: res.datasets,
            }),
          ),
        ),
      ),
    );
  });

  points$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.dateRangeChanged,
        DashboardActions.togglePoint,
        DashboardActions.togglePointVariable,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectPoints),
        this.store.select(
          dashboardFeature.selectCacheByType(DashboardCacheType.points),
        ),
      ]),
      exhaustMap(([, dateRange, points, cache]) =>
        this.doEnsureCache(DashboardCacheType.points, () =>
          this.dashboardMng.ensurePointsCache(dateRange, points, cache),
        ),
      ),
    );
  });

  togglePointAfterFocus$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.focusDevice),
      filter((action) => !!action.points.length),
      map((action) =>
        // TODO: Dispatch multiple actions here, one per action.points array element
        DashboardActions.togglePoint({ point: action.points[0] }),
      ),
    );
  });

  toggleImagesAfterFocus$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.focusDevice),
      filter((action) => !!action.devices.some((d) => !!d.thermal_camera_id)),
      map(() => DashboardActions.toggleImagesDisplay()),
    );
  });

  customPoints$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.dateRangeChanged,
        DashboardActions.toggleCustomPoint,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectCustomPoints),
        this.store.select(
          dashboardFeature.selectCacheByType(DashboardCacheType.customPoints),
        ),
      ]),
      exhaustMap(([, dateRange, customPoints, cache]) =>
        this.doEnsureCache(DashboardCacheType.customPoints, () =>
          this.dashboardMng.ensureCustomPointsCache(
            dateRange,
            customPoints,
            cache,
          ),
        ),
      ),
    );
  });

  recognitionReadings$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.dateRangeChanged,
        DashboardActions.toggleRecognition,
        DashboardActions.toggleRecognitionVariable,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectRecognitions),
        this.store.select(
          dashboardFeature.selectCacheByType(
            DashboardCacheType.recognitionReadings,
          ),
        ),
      ]),
      exhaustMap(([, dateRange, recognitions, cache]) =>
        this.doEnsureCache(DashboardCacheType.recognitionReadings, () =>
          this.dashboardMng.ensureRecognitionReadingsCache(
            dateRange,
            recognitions,
            cache,
          ),
        ),
      ),
    );
  });

  cameraImagesDashboard$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.dateRangeChanged,
        DashboardActions.toggleDevice,
        DashboardActions.toggleImagesDisplay,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectImagesDisplayed),
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectDevices),
        this.store.select(
          dashboardFeature.selectCacheByType(DashboardCacheType.images),
        ),
      ]),
      filter(([, imagesDisplayed]) => imagesDisplayed),
      exhaustMap(([, , dateRange, devices, cache]) =>
        this.doEnsureCache(DashboardCacheType.images, () =>
          this.dashboardMng.ensureImagesCache(dateRange, devices, cache),
        ),
      ),
    );
  });

  cameraImagesStandalone$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.refreshStandaloneCameraImages),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
        this.store.select(
          dashboardFeature.selectCacheByType(DashboardCacheType.images),
        ),
      ]),
      exhaustMap(([, devices, cache]) =>
        this.doEnsureCache(DashboardCacheType.images, () =>
          this.dashboardMng.ensureImagesCache(
            dateRanges.find(
              (dr) => dr.name === DateRangesPredefined.last24hours,
            )!,
            devices,
            cache,
          ),
        ),
      ),
    );
  });

  cameraAreas$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.dateRangeChanged,
        DashboardActions.toggleDevice,
        DashboardActions.toggleImagesDisplay,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectImagesDisplayed),
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectRecognitions),
        this.store.select(
          dashboardFeature.selectCacheByType(
            DashboardCacheType.recognitionAreas,
          ),
        ),
      ]),
      filter(([, imagesDisplayed]) => imagesDisplayed),
      exhaustMap(([, , dateRange, recognitions, cache]) =>
        this.doEnsureCache(DashboardCacheType.recognitionAreas, () =>
          this.dashboardMng.ensureRecognitionAreasCache(
            dateRange,
            recognitions,
            cache,
          ),
        ),
      ),
    );
  });

  toggleImagesDisplay$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.toggleDevice,
        DashboardActions.toggleImagesDisplay,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectImagesDisplayed),
        this.store.select(dashboardFeature.isAnyCameraSelected),
      ]),
      filter(
        ([, imagesDisplayed, camerasSelected]) =>
          imagesDisplayed && !camerasSelected,
      ),
      map(() => DashboardActions.toggleImagesDisplay()),
    );
  });

  cacheUpdated$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.cacheUpdated),
      map((action) => {
        switch (action.key) {
          case DashboardCacheType.images:
          case DashboardCacheType.recognitionAreas:
            return DashboardActions.refreshDashboardCameraImages();
          default:
            return DashboardActions.regenerateChart();
        }
      }),
    );
  });

  imagesCacheUpdated$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.cacheUpdated),
      filter((action) => action.key === DashboardCacheType.images),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDashboardState),
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
      ]),
      switchMap(([, dashboardState, devices]) =>
        from(
          this.dashboardMng.getStandaloneCameraImagesToDisplay(
            dashboardState.cache,
            devices,
          ),
        ).pipe(
          map((images) =>
            DashboardActions.refreshStandaloneCameraImagesSuccess({ images }),
          ),
        ),
      ),
    );
  });

  regenerateChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.regenerateChart,
        DashboardActions.toggleDevice,
        DashboardActions.toggleVariable,
        DashboardActions.focusDevice,
        DashboardActions.chartUomConfigChanged,
        DashboardActions.chartCommentsFetched,
        DashboardActions.chartCommentSaveSucceeded,
        DashboardActions.chartCommentDeleted,
        DashboardActions.toggleMinimizedComments,
        DashboardActions.toggleCondensation,
        NodesActions.nodeUpdated,
        AdminActions.variableConfigUpdated,
      ),
      debounce(() => interval(300)),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDashboardState),
        this.store.select(adminFeature.selectVariables),
      ]),
      map(([, dashboardState, hostVariables]) =>
        DashboardActions.regenerateChartSuccess({
          result: this.dashboardMng.generateChart(
            dashboardState,
            hostVariables,
          ),
        }),
      ),
    );
  });

  userSettingsChangeSuccess$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(AdminActions.userSettingsChangeSuccess),
      delay(1000),
      map(() => DashboardActions.refresh()),
    );
  });

  refresh$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.refresh),
      concatLatestFrom(() =>
        this.store.select(dashboardFeature.selectDateRange),
      ),
      map(([, dateRange]) => DashboardActions.dateRangeChanged({ dateRange })),
    );
  });

  refreshCameraImages$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.refreshDashboardCameraImages,
        DashboardActions.toggleDevice,
        DashboardActions.chartHover,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDashboardState),
      ]),
      switchMap(([, dashboardState]) =>
        from(
          this.dashboardMng.getDashboardCameraImagesToDisplay(dashboardState),
        ).pipe(
          map((images) =>
            DashboardActions.refreshDashboardCameraImagesSuccess({ images }),
          ),
        ),
      ),
    );
  });

  // TODO: can lead to multiple chart redraw and even extra API calls, refactor this
  catchUpDateRangeChanged$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.applyChartFavourite,
        DashboardActions.focusChartComment,
        DashboardActions.applyChartReportData,
      ),
      map(() => DashboardActions.reapplyCurrentDateRange()),
    );
  });

  reapplyCurrentDateRange$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.reapplyCurrentDateRange),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectDateRange),
      ]),
      map(([, dateRange]) => DashboardActions.dateRangeChanged({ dateRange })),
    );
  });

  restoreSavedConfigs$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(AuthActions.navigateToApp),
      filter((action) => !action.ignoreSideEffects),
      map(() => localStorage.getItem(this.savedConfigsPersistentKey)),
      filter((savedConfigsRaw) => !!savedConfigsRaw),
      map((savedConfigsRaw) =>
        DashboardActions.restoreSavedConfigs({
          savedConfigs: JSON.parse(savedConfigsRaw ?? ''),
        }),
      ),
    );
  });

  //#region Chart Favourites

  uploadRestoredSavedConfigs$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.restoreSavedConfigs),
      switchMap((action) =>
        forkJoin(
          action.savedConfigs.map((config) =>
            this.dataApi.chartsFavouriteCreate(
              config.locationId,
              new ChartsFavouritePostRequest({
                name: config.name,
                date_range_name: config.dateRangeName,
                is_default: config.default,
                configs: config.config,
              }),
            ),
          ),
        ).pipe(
          tap(() =>
            localStorage.setItem(
              this.savedConfigsPersistentKeyBU,
              JSON.stringify(action.savedConfigs),
            ),
          ),
          tap(() => localStorage.removeItem(this.savedConfigsPersistentKey)),
          map(() => DashboardActions.fetchChartFavorites()),
        ),
      ),
    );
  });

  fetchChartFavorites$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        // TODO: improve to not fetch favorites on every action
        AdminActions.devicesPointsUpdated,
        DashboardActions.fetchChartFavorites,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
        this.store.select(adminFeature.selectDevicePoints),
      ]),
      filter(
        (values) =>
          // TODO: remove this condition when chart-snapshot is refactored
          window.location.pathname !== '/reports/chart-snapshot' &&
          values.every((v) => !!v),
      ),
      switchMap(([, location, devices, points]) =>
        this.dataApi
          .chartsFavouriteRetrieve(location!.location!.central_id)
          .pipe(
            map((res) =>
              DashboardActions.fetchChartFavoritesSuccess({
                items: res.charts_favourites.map((item) => ({
                  ...item,
                  configs: {
                    ...item.configs,
                    devices: devices.filter((d) =>
                      (item.configs['devices'] ?? []).some(
                        (cd: IConfiguredDevice) =>
                          d.remote_id === cd.remote_id &&
                          d.thermal_camera_id === cd.thermal_camera_id,
                      ),
                    ),
                    points: points.filter((p) =>
                      (item.configs['points'] ?? []).some(
                        (cp: IGetCurrentCamPoint) => p.id === cp.id,
                      ),
                    ),
                  },
                })),
              }),
            ),
          ),
      ),
    );
  });

  createChartFavourite$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.createChartFavourite),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(dashboardFeature.selectDateRange),
        this.store.select(dashboardFeature.selectDashboardState),
      ]),
      switchMap(([action, location, dateRange, state]) =>
        this.dataApi
          .chartsFavouriteCreate(
            location!.location!.central_id,
            new ChartsFavouritePostRequest({
              name: action.name,
              date_range_name: dateRange.name,
              is_default: false,
              configs: DashboardUtils.getDashboardConfigFromState(state),
            }),
          )
          .pipe(
            map((res) =>
              DashboardActions.createChartFavouriteSuccess({
                item: res.charts_favourite as any,
              }),
            ),
          ),
      ),
    );
  });

  updateChartFavourite$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          DashboardActions.updateChartFavourite,
          DashboardActions.toggleChartFavouriteDefault,
        ),
        concatLatestFrom(() => [
          this.store.select(adminFeature.selectActiveLocation),
          this.store.select(dashboardFeature.selectChartFavourites),
        ]),
        switchMap(([action, location, chartFavourites]) =>
          this.dataApi.chartsFavouriteCreate(
            location!.location!.central_id,
            new ChartsFavouritePostRequest(
              chartFavourites.find((cfs) => cfs.id === action.id),
            ),
          ),
        ),
      );
    },
    { dispatch: false },
  );

  deleteChartFavourite$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.deleteChartFavourite),
        concatLatestFrom(() => [
          this.store.select(adminFeature.selectActiveLocation),
        ]),
        switchMap(([action, location]) =>
          this.dataApi.chartsFavouriteDestroy(
            location!.location!.central_id,
            action.id,
          ),
        ),
      );
    },
    { dispatch: false },
  );

  applyDefaultChartFavourite$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.fetchChartFavoritesSuccess),
      concatLatestFrom(() => [
        this.store.select(
          dashboardFeature.selectChartFavouritesForActiveLocation,
        ),
        this.store.select(dashboardFeature.selectChatJsBinding),
      ]),
      filter(([, , chatJsBinding]) => !chatJsBinding.data.datasets.length),
      map(([, chartFavourites]) => chartFavourites.find((cf) => cf.is_default)),
      map((defaultChartFavourite) =>
        defaultChartFavourite
          ? DashboardActions.applyChartFavourite({
              id: defaultChartFavourite!.id,
            })
          : DashboardActions.restoreLatestChartState(),
      ),
    );
  });

  //#endregion

  chartTooltip$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.chartHover),
        filter((action) => !!action.timestamp),
        concatLatestFrom(() => [
          this.store.select(dashboardFeature.selectChartData),
        ]),
        tap(([action, chartData]) =>
          this.dashboardMng.generateTooltips(
            action.timestamp!,
            action.clientX,
            action.clientY,
            chartData.data.datasets,
          ),
        ),
      );
    },
    { dispatch: false },
  );

  automaticallySelectCameraRecognition$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.toggleDevice),
        concatLatestFrom((action) => [
          this.store.select(dashboardFeature.selectDashboardState),
          this.store.select(
            adminFeature.selectDeviceSettings(action.device.thermal_camera_id),
          ),
        ]),
        filter(
          ([action, state, settings]) =>
            state.devices.some(
              (d) => d.thermal_camera_id === action.device.thermal_camera_id,
            ) && !!settings?.flower_recognition,
        ),
        tap(([action, state]) =>
          this.dashboardMng.ensureCameraRecognitionsPreselected(
            action.device,
            state,
          ),
        ),
      );
    },
    { dispatch: false },
  );

  shiftDateRange$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.shiftDateRange),
      concatLatestFrom(() =>
        this.store.select(dashboardFeature.selectDateRange),
      ),
      map(([action, dateRange]) =>
        DateUtils.getShiftedDateRange(dateRange, action.delta),
      ),
      map((newRange) =>
        DashboardActions.dateRangeChanged({
          dateRange: newRange,
        }),
      ),
    );
  });

  autoRefresh$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(AuthActions.navigateToApp),
      switchMap(() =>
        interval(60000).pipe(map(() => DashboardActions.refresh())),
      ),
    );
  });

  chartClick$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.chartClick),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectChartData),
      ]),
      switchMap(([action, chartData]) =>
        from(
          this.dashboardMng.onChartClick(
            chartData.data.datasets,
            action.chartX,
          ),
        ).pipe(
          filter((commentInput) => !!commentInput),
          map((commentInput) =>
            DashboardActions.chartCommentAdded({
              comment: {
                chartX: action.chartX,
                chartY: action.chartY,
                text: commentInput!.text,
                color: commentInput!.color,
              },
            }),
          ),
        ),
      ),
    );
  });

  loadChartComments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(AdminActions.activeLocationConfigRetrived),
      concatLatestFrom(() =>
        this.store.select(adminFeature.selectActiveLocation),
      ),
      filter((values) => values.every((v) => !!v)),
      switchMap(([, activeLocation]) =>
        this.dataApi
          .chartsCommentRetrieve(activeLocation!.location!.central_id)
          .pipe(
            map((res) =>
              DashboardActions.chartCommentsFetched({
                comments: res.charts_comments
                  .filter((c) => !!c.unix_timestamp)
                  .sort((c1, c2) => c2.unix_timestamp - c1.unix_timestamp),
              }),
            ),
          ),
      ),
    );
  });

  saveChartComments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.chartCommentAdded),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(dashboardFeature.selectDashboardState),
      ]),
      filter((values) => values.every((v) => !!v)),
      switchMap(([action, activeLocation, state]) =>
        this.dataApi
          .chartsCommentCreate(
            activeLocation!.location!.central_id,
            new ChartsCommentPostRequest({
              comment: action.comment.text,
              color: action.comment.color,
              unix_timestamp: Math.floor(action.comment.chartX / 1000),
              reading: action.comment.chartY,
              configs: {
                dashboardConfig:
                  DashboardUtils.getDashboardConfigFromState(state),
                startTimestamp: state.dateRange.start().valueOf(),
                endTimestamp: state.dateRange.end().valueOf(),
              } satisfies IChartCommentConfig,
            }),
          )
          .pipe(
            map((res) =>
              DashboardActions.chartCommentSaveSucceeded({
                comment: res.charts_comment,
              }),
            ),
          ),
      ),
    );
  });

  deleteChartComments$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.chartCommentDeleted),
        concatLatestFrom(() => [
          this.store.select(adminFeature.selectActiveLocation),
        ]),
        filter((values) => values.every((v) => !!v)),
        switchMap(([action, activeLocation]) =>
          this.dataApi.chartsCommentDestroy(
            activeLocation!.location!.central_id,
            action.id,
          ),
        ),
      );
    },
    { dispatch: false },
  );

  downloadChartImage$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.downloadChartImage),
        concatLatestFrom(() => [
          this.store.select(adminFeature.selectActiveLocation),
          this.store.select(dashboardFeature.selectDashboardState),
        ]),
        switchMap(([, location, state]) => {
          const { date_begin: startTimestamp, date_end: endTimestamp } =
            this.dashboardMng.getApiDateRange(state.dateRange);
          return this.dataApi
            .chartSnapshotCreate(
              new ChartSnapshotRequest({
                data: DashboardUtils.encodeChartReportDataUri(
                  location!.location!.central_id,
                  startTimestamp,
                  endTimestamp,
                  state,
                ),
              }),
            )
            .pipe(
              tap((res) => {
                const link = document.createElement('a');
                link.href = res.snapshotUrl;
                link.download = 'sigrow-chart.png';
                link.click();
              }),
            );
        }),
      );
    },
    { dispatch: false },
  );

  updateChartRangeStats$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        DashboardActions.calculateChartRangeStats,
        DashboardActions.regenerateChartSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(dashboardFeature.selectChartData),
        this.store.select(dashboardFeature.selectChartRange),
      ]),
      filter(([, chartData, chartRange]) => !!chartData && !!chartRange),
      map(([, chartData, chartRange]) => {
        const stats = this.dashboardService.getChartRangeStats(chartData.data, {
          startTimestamp: chartRange!.startTimestamp,
          endTimestamp: chartRange!.endTimestamp,
        });
        if (stats) {
          return DashboardActions.updateChartRangeStats({ stats });
        }
        // If no stats available, don't dispatch an action
        return { type: '[Dashboard] No Stats Available' };
      }),
    );
  });

  persistLatestChartState$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(DashboardActions.regenerateChartSuccess),
        concatLatestFrom(() => [
          this.store.select(adminFeature.selectActiveLocation),
          this.store.select(dashboardFeature.selectDateRange),
          this.store.select(dashboardFeature.selectDashboardState),
        ]),
        tap(([, location, dateRange, state]) =>
          localStorage.setItem(
            this.latestChartStatePersistentKey,
            JSON.stringify({
              locationId: location!.location!.central_id,
              dateRangeName: dateRange.name,
              configs: DashboardUtils.getDashboardConfigFromState(state),
            } satisfies ILatestChartState),
          ),
        ),
      );
    },
    { dispatch: false },
  );

  restoreLatestChartState$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(DashboardActions.restoreLatestChartState),
      map(() => localStorage.getItem(this.latestChartStatePersistentKey)),
      filter((chartState) => !!chartState),
      map((chartState) => JSON.parse(chartState ?? '') as ILatestChartState),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
      ]),
      filter(
        ([chartState, activeLocation]) =>
          chartState.locationId === activeLocation!.location!.central_id,
      ),
      map(([chartState]) =>
        DashboardActions.applyLatestChartState({
          state: chartState,
        }),
      ),
    );
  });

  constructor(
    private actions$: Actions,
    private store: Store,
    private dashboardMng: DashboardService,
    private dashboardService: DashboardService,
    private dataApi: DataApi,
    private route: ActivatedRoute,
  ) {}

  private doEnsureCache(
    type: DashboardCacheType,
    ensureFunction: () => Promise<Map<string, unknown>>,
  ) {
    return from(ensureFunction()).pipe(
      map((cache) =>
        DashboardActions.cacheUpdated({
          key: type,
          value: cache,
        }),
      ),
    );
  }
}
