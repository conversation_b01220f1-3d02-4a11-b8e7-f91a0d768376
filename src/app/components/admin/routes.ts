import { Route } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { MasterComponent } from './_master/master.component';
import { BiomassEffects } from './biomass/state/biomass.effects';
import { biomassFeature } from './biomass/state/biomass.feature';
import { CamerasComponent } from './dashboard/components/cameras/cameras.component';
import { DashboardComponent } from './dashboard/components/dashboard/dashboard.component';
import { HeatmapEffects } from './heatmap/state/heatmap.effects';
import { heatmapFeature } from './heatmap/state/heatmap.feature';
import { NodesComponent } from './nodes/components/nodes/nodes.component';
import { ReadingsComponent } from './nodes/components/readings/readings.component';
import { RTREffects } from './rtr/state/rtr.effects';
import { rtrFeature } from './rtr/state/rtr.feature';
import { SettingsComponent } from './settings/components/settings/settings.component';
import { TranspirationEffects } from './transpiration/state/transpiration.effects';
import { transpirationFeature } from './transpiration/state/transpiration.feature';

export const ADMIN_ROUTES: Route[] = [
  {
    path: '',
    pathMatch: 'prefix',
    component: MasterComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'map',
      },
      {
        path: 'map',
        component: NodesComponent,
      },
      {
        path: 'charts',
        component: DashboardComponent,
      },
      {
        path: 'cameras',
        component: CamerasComponent,
      },
      {
        path: 'table',
        component: ReadingsComponent,
      },
      {
        path: 'settings',
        component: SettingsComponent,
      },
      {
        path: 'rtr',
        loadComponent: () =>
          import('./rtr/components/rtr/rtr.component').then(
            (c) => c.RtrComponent,
          ),
        providers: [provideState(rtrFeature), provideEffects(RTREffects)],
      },
      {
        path: 'transpiration',
        loadComponent: () =>
          import(
            './transpiration/components/transpiration/transpiration.component'
          ).then((c) => c.TranspirationComponent),
        providers: [
          provideState(transpirationFeature),
          provideEffects(TranspirationEffects),
        ],
      },
      {
        path: 'biomass',
        loadComponent: () =>
          import('./biomass/components/_home/home.component').then(
            (c) => c.BiomassHomeComponent,
          ),
        providers: [
          provideState(biomassFeature),
          provideEffects(BiomassEffects),
        ],
      },
      {
        path: 'heatmap',
        loadComponent: () =>
          import('./heatmap/components/heatmap/heatmap.component').then(
            (c) => c.HeatmapComponent,
          ),
        providers: [
          provideState(heatmapFeature),
          provideEffects(HeatmapEffects),
        ],
      },
    ],
  },
];
