<ng-container *ngrxLet="activeLocation$ as activeLocation">
  <div class="p-6">
    <div class="flex flex-wrap -mx-3">
      <div class="w-full lg:w-1/3 px-3 mb-6">
        <div
          class="flex flex-col rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark"
        >
          <div class="mb-6 text-xl font-medium text-gray-900">
            {{ "userSettings" | translate }}
          </div>

          <ng-container [formGroup]="userSettingsForm">
            <mat-form-field>
              <mat-label>{{ "language" | translate }}</mat-label>
              <mat-select formControlName="language">
                @for (language of languages; track language) {
                  <mat-option [value]="language.id">{{
                    language.name
                  }}</mat-option>
                }
              </mat-select>
            </mat-form-field>

            <ng-container formGroupName="alerts">
              <div class="mb-4">
                <mat-slide-toggle
                  formControlName="nodes_status_warning_alerts"
                  labelPosition="before"
                  color="primary"
                >
                  <span class="text-base font-medium me-4">
                    {{ "nodesStatusWarningAlerts" | translate }}
                  </span>
                </mat-slide-toggle>
                <div
                  [innerHtml]="'statusAlerts' | translate"
                  class="mt-1 text-gray-500"
                ></div>
              </div>

              <div class="mb-6">
                <mat-slide-toggle
                  formControlName="nodes_offline_alerts"
                  labelPosition="before"
                  color="primary"
                >
                  <span class="text-base font-medium me-4">
                    {{ "nodesOfflineAlerts" | translate }}</span
                  >
                </mat-slide-toggle>
                <div class="mt-1 text-gray-500">
                  {{ "nodesOfflineAlertsWarnAll" | translate }}
                </div>
              </div>
            </ng-container>
          </ng-container>

          <div class="mb-6">
            <ng-container *ngrxLet="apiKey$ as apiKey">
              <mat-form-field class="w-full">
                <mat-label>{{ "apiv3Key" | translate }}</mat-label>
                <input [value]="apiKey" matInput readonly />
                <button (click)="regenerateApiKey()" mat-icon-button matSuffix>
                  <mat-icon>autorenew</mat-icon>
                </button>
              </mat-form-field>
              <div class="-mt-4 ms-1 text-gray-500">
                {{ "youCanGenerateAPIv3Key" | translate }}
              </div>
            </ng-container>
          </div>

          <button
            mat-stroked-button
            (click)="resetApplication()"
            color="primary"
          >
            {{ "resetApplication" | translate }}
          </button>
          <div class="ms-1 mt-2 text-gray-500">
            {{ "thisActionClearsCache" | translate }}
          </div>
        </div>
      </div>

      <div class="w-full lg:w-1/3 px-3 mb-6">
        <div
          class="flex flex-col rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark"
        >
          <div class="mb-6 text-xl font-medium text-gray-900">
            {{ "locationSettings" | translate }}
          </div>

          <mat-form-field>
            <mat-label>{{ "centralId" | translate }}</mat-label>
            <input
              matInput
              disabled
              [value]="activeLocation?.location?.central_id"
            />
          </mat-form-field>

          <ng-container [formGroup]="locationConfigForm">
            <mat-form-field>
              <mat-label>{{ "locationName" | translate }}</mat-label>
              <input formControlName="location_name" matInput maxlength="15" />
            </mat-form-field>

            <mat-form-field>
              <mat-label>{{ "timezone" | translate }}</mat-label>
              <ng-container *ngrxLet="timezones$ as timezones">
                <mat-select formControlName="location_timezone_id">
                  <mat-option>
                    <ngx-mat-select-search
                      [formControl]="timezoneFilterCtrl"
                      placeholderLabel="Search"
                      noEntriesFoundLabel="Please refine your search criteria"
                    ></ngx-mat-select-search>
                  </mat-option>
                  @for (timezone of timezones; track timezone) {
                    <mat-option [value]="timezone.id">{{
                      timezone.name
                    }}</mat-option>
                  }
                </mat-select>
              </ng-container>
            </mat-form-field>

            <mat-form-field>
              <mat-label>{{ "mapColumnsCount" | translate }}</mat-label>
              <input formControlName="nodemapx" matInput type="number" />
            </mat-form-field>

            <ng-container formGroupName="location_alerts">
              <div class="ms-1 mb-4">
                <mat-slide-toggle
                  formControlName="nodes_status_warning_alerts"
                  labelPosition="before"
                  color="primary"
                >
                  <span class="text-base font-medium me-4">
                    {{ "nodesStatusWarningAlerts" | translate }}
                  </span>
                </mat-slide-toggle>
                <div
                  [innerHTML]="'statusAlertsWarn' | translate"
                  class="mt-1 text-gray-500"
                ></div>
              </div>

              <div class="ms-1">
                <mat-slide-toggle
                  formControlName="nodes_offline_alerts"
                  labelPosition="before"
                  color="primary"
                >
                  <span class="text-base font-medium me-4">
                    {{ "nodesOfflineAlerts" | translate }}
                  </span>
                </mat-slide-toggle>
                <div class="mt-1 text-gray-500">
                  {{ "nodesOfflineAlertsWarnAll" | translate }}
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
      </div>

      <div class="w-full lg:w-1/3 px-3 mb-6">
        <div
          class="flex flex-col rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark"
        >
          <div class="mb-6 text-xl font-medium text-gray-900">
            {{ "sensorReadingsAlerts" | translate }}
          </div>

          <div class="mb-6 text-gray-500">
            <p>
              {{ "subscribeReadingsNotifications" | translate }}
            </p>
            <p>
              {{ "noSensorsWithinInstallation" | translate }}
            </p>
            <p>
              {{ "sentHourly" | translate }}
            </p>
            <p [innerHTML]="'smsAlerts' | translate" class="!mb-0"></p>
          </div>

          @if (activeLocation && activeLocation.configuration) {
            <mat-form-field>
              <mat-label>{{ "alertEmails" | translate }}</mat-label>
              <mat-chip-grid #chipGrid aria-label="Enter fruits">
                @for (
                  email of activeLocation.configuration.alert_emails;
                  track email
                ) {
                  <mat-chip-row (removed)="remove(email)">
                    {{ email.email | lowercase }}
                    <button matChipRemove>
                      <mat-icon>cancel</mat-icon>
                    </button>
                  </mat-chip-row>
                }
                <input
                  placeholder="{{ 'addEmail' | translate }}..."
                  [matChipInputFor]="chipGrid"
                  [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                  [matChipInputAddOnBlur]="true"
                  (matChipInputTokenEnd)="add($event)"
                />
              </mat-chip-grid>
            </mat-form-field>

            <mat-form-field>
              <mat-label>{{ "alertPhone" | translate }}</mat-label>
              <input
                [disabled]="!activeLocation.configuration!.sms_alerts_enabled"
                matInput
                type="tel"
              />
            </mat-form-field>
          }

          <div class="relative overflow-x-auto">
            <table
              class="w-full text-sm text-left rtl:text-right text-gray-500"
            >
              <thead class="text-xs uppercase bg-[#F9FAFB] h-14">
                <tr>
                  <th scope="col" class="px-6 py-3">
                    {{ "variables" | translate }}
                  </th>
                  <th scope="col" class="px-6 py-3">
                    {{ "min" | translate }}
                  </th>
                  <th scope="col" class="px-6 py-3">
                    {{ "max" | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngrxLet="variables$ as variables">
                  @for (variable of variables; track variable.name) {
                    <tr class="bg-white border-b border-[#EEEEEE]">
                      <th
                        scope="row"
                        class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap"
                      >
                        {{ variable.name | translate }}
                      </th>
                      <td class="px-6 py-4">
                        <input
                          [value]="variable.min"
                          (change)="setAlertMin(variable, $event)"
                          type="number"
                          class="w-full outline-none"
                        />
                      </td>
                      <td class="px-6 py-4">
                        <input
                          [value]="variable.max"
                          (change)="setAlertMax(variable, $event)"
                          type="number"
                          class="w-full outline-none"
                        />
                      </td>
                    </tr>
                  }
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<div class="fixed left-6 bottom-4">v{{ appVersion }}</div>
