import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatChipInputEvent } from '@angular/material/chips';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable, combineLatest, filter, map, startWith, tap } from 'rxjs';
import {
  ILocationConfigurationEmail,
  ITimeZone,
  IVariableToDisplay,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { AuthActions } from './../../../../../state/auth/actions';

import { isNumber } from 'lodash';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { APP_VERSION } from '../../../../../app.config';
import { ISigrowLocation } from '../../../../../model/admin';
import { adminFeature } from '../../../../../state/admin/feature';
import { ValidationUtils } from '../../../../../utils/validation';
import { AdminActions } from './../../../../../state/admin/actions';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxMatSelectSearchModule,
    LetDirective,
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss',
})
export class SettingsComponent extends BaseComponent implements OnInit {
  variables$!: Observable<IVariableToDisplay[]>;
  timezones$!: Observable<ITimeZone[]>;
  apiKey$!: Observable<string>;
  activeLocation$!: Observable<ISigrowLocation | undefined>;

  timezoneFilterCtrl = new FormControl('');

  userSettingsForm = new FormGroup(
    {
      time_zone_id: new FormControl<number>(0),
      language: new FormControl<string>(''),
      alerts: new FormGroup({
        nodes_status_warning_alerts: new FormControl<boolean>(false),
        nodes_offline_alerts: new FormControl<boolean>(false),
      }),
    },
    {
      updateOn: 'blur',
    },
  );

  locationConfigForm = new FormGroup(
    {
      location_name: new FormControl<string>(''),
      nodemapx: new FormControl<number>(0),
      nodemapy: new FormControl<number>(0),
      location_alerts: new FormGroup({
        nodes_status_warning_alerts: new FormControl<boolean>(false),
        nodes_offline_alerts: new FormControl<boolean>(false),
      }),
      location_timezone_id: new FormControl<number>(0),
    },
    {
      updateOn: 'blur',
    },
  );

  appVersion = inject(APP_VERSION);

  readonly languages = [
    { id: 'en', name: 'English' },
    { id: 'es', name: 'Español' },
    { id: 'nl', name: 'Nederlands' },
  ];

  readonly separatorKeysCodes = [COMMA, ENTER] as const;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.variables$ = this.store.select(adminFeature.selectVariables);
    this.apiKey$ = this.store.select(adminFeature.selectApiKey);
    this.activeLocation$ = this.store
      .select(adminFeature.selectActiveLocation)
      .pipe(
        filter((activeLocation) => !!activeLocation?.location?.name),
        tap((activeLocation) =>
          this.locationConfigForm.reset(activeLocation?.configuration, {
            emitEvent: false,
          }),
        ),
      );

    this.timezones$ = combineLatest([
      this.store.select(adminFeature.selectTimezones),
      this.timezoneFilterCtrl.valueChanges.pipe(startWith('')),
    ]).pipe(
      map(([timezones, filter]) =>
        filter
          ? timezones.filter(
              (tz) => tz.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0,
            )
          : timezones,
      ),
    );

    this.subSafe(this.userSettingsForm.valueChanges, (settings) =>
      this.store.dispatch(AdminActions.userSettingsChanged({ settings })),
    );

    this.subSafe(this.locationConfigForm.valueChanges, (config) => {
      if (isNumber(config.nodemapx)) {
        if (config.nodemapx <= 0) {
          config.nodemapx = 1;
        }
        if (config.nodemapx > 10) {
          config.nodemapx = 10;
        }
      }
      this.store.dispatch(AdminActions.updateActiveLocationConfig({ config }));
    });

    this.subSafe(
      this.store.select(adminFeature.selectUserSettings).pipe(
        filter((settings) => !!settings.user_id),
        tap((settings) =>
          this.userSettingsForm.reset(settings, { emitEvent: false }),
        ),
      ),
    );
  }

  regenerateApiKey() {
    this.store.dispatch(AdminActions.regenerateApiKey());
  }

  add(event: MatChipInputEvent) {
    const emailAddress = event.value;
    if (ValidationUtils.validEmail(emailAddress)) {
      this.store.dispatch(AdminActions.addEmailAlert({ emailAddress }));
    } else if (emailAddress) {
      this.ui.showErrorMessage('Please enter valid email address');
    }
    event.chipInput!.clear();
  }

  remove(email: ILocationConfigurationEmail) {
    this.store.dispatch(AdminActions.alertEmailRemoved({ email }));
  }

  async resetApplication() {
    if (!(await this.ui.confirm())) {
      return;
    }

    this.store.dispatch(AuthActions.logout());
  }

  setAlertMin(variable: IVariableToDisplay, event: Event) {
    this.updateAlertConfig(variable, {
      min: +(event.target as HTMLInputElement).value || undefined,
    });
  }

  setAlertMax(variable: IVariableToDisplay, event: Event) {
    this.updateAlertConfig(variable, {
      max: +(event.target as HTMLInputElement).value || undefined,
    });
  }

  private updateAlertConfig(
    variable: IVariableToDisplay,
    changes: Partial<IVariableToDisplay>,
  ) {
    this.store.dispatch(
      AdminActions.variableConfigUpdated({
        variable: {
          ...variable,
          ...changes,
        },
      }),
    );
  }
}
