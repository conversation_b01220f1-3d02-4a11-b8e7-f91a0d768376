import { Component, Input } from '@angular/core';
import SharedModules from '../../../../../common/shared.modules';
import { AlertType } from '../../../../../model/alert';

@Component({
  selector: 'app-variable-value',
  standalone: true,
  imports: [...SharedModules],
  templateUrl: './variable-value.component.html',
  styleUrls: [],
})
export class VariableValueComponent {
  @Input() value!: any;
  @Input() uom?: string;
  @Input() alertType?: AlertType;

  alertTypes = AlertType;
}
