<button mat-icon-button mat-dialog-close class="!absolute right-2 top-2 !z-10">
  <mat-icon>close</mat-icon>
</button>
<h2
  mat-dialog-title
  class="text-center border-b border-stroke !pb-4 !mb-0 !px-12 lg:px-4"
>
  Map order
</h2>
<mat-dialog-content class="!p-4 !pb-0">
  <div class="w-[70vw] h-[70vh]">
    <div cdkDropList (cdkDropListDropped)="drop($event)" class="w-full h-full">
      @for (nodeItem of nodeList; track nodeItem) {
        <div
          cdkDrag
          class="w-full md:w-1/2 lg:w-1/2 xl:w-1/3 px-3 mb-5 bg-white"
        >
          <div
            class="cursor-pointer rounded-sm border border-stroke bg-white p-4 shadow-default md:p-6 xl:p-7.5"
          >
            <h4 class="mb-2 font-medium flex items-center">
              @if (nodeItem.cameraId) {
                <mat-icon class="me-1.5 text-neutral-600">camera</mat-icon>
                {{ "camera" | translate }} {{ nodeItem.cameraId }} /
              }
              {{ nodeItem.id }}
            </h4>
            <h3 class="text-title-md font-bold text-black dark:text-white">
              {{ nodeItem.name }}
            </h3>
          </div>
        </div>
      }
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-button mat-dialog-close>{{ "close" | translate }}</button>
</mat-dialog-actions>
