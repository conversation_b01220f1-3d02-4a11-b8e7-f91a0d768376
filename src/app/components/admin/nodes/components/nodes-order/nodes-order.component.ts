import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { Component, OnInit } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash';
import { lastValueFrom, take } from 'rxjs';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { INodeListItem } from '../../../../../model/node';
import { nodesFeature } from '../../state/nodes.feature';

@Component({
  selector: 'app-nodes-order',
  standalone: true,
  imports: [...SharedModules, LetDirective, CdkDropList, CdkDrag],
  templateUrl: './nodes-order.component.html',
  styleUrl: './nodes-order.component.scss',
})
export class NodesOrderComponent extends BaseComponent implements OnInit {
  nodeList!: INodeListItem[];

  constructor(private store: Store) {
    super();
  }

  async ngOnInit() {
    this.nodeList = cloneDeep(
      await lastValueFrom(
        this.store.select(nodesFeature.selectNodeList).pipe(take(1)),
      ),
    );
  }

  drop(event: CdkDragDrop<INodeListItem[]>) {
    moveItemInArray(this.nodeList, event.previousIndex, event.currentIndex);
  }
}
