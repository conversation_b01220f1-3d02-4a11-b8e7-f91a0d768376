<ng-container
  *ngrxLet="{
    currentNode: currentNode$,
    variables: variables$,
    isMobile: ui.isMobile$
  } as state"
>
  <button
    mat-icon-button
    mat-dialog-close
    class="!absolute right-2 top-2 !z-10"
  >
    <mat-icon>close</mat-icon>
  </button>
  <h2
    mat-dialog-title
    class="text-center border-b border-stroke !pb-4 !mb-0 !px-12 lg:px-4"
  >
    {{ "remoteIDdevice" | translate }}
    {{ state.currentNode.nodeData?.remote_id }} /
    {{ state.currentNode.nodeData?.node_name }}
    @if (state.currentNode.nodeItem.cameraId) {
      / {{ "camera" | translate }} {{ state.currentNode.nodeItem.cameraId }}
    }
  </h2>
  <mat-dialog-content
    [ngClass]="{ '!pt-2': state.isMobile }"
    class="!p-4 !pb-0 mat-typography"
  >
    <div
      class="absolute left-0 top-0 h-full flex justify-center items-center ps-1 lg:ps-5"
    >
      <button mat-mini-fab (click)="scrollNodes(-1)" color="light">
        <mat-icon>arrow_back</mat-icon>
      </button>
    </div>
    <div class="flex flex-wrap px- lg:px-16">
      <dl class="divide-y divide-gray-100 w-full px-6 lg:ps-2 lg:pe-5 lg:w-1/2">
        @if (state.currentNode.nodeItem) {
          @for (attr of state.currentNode.nodeItem.attributes; track attr) {
            @if (
              !state.isMobile ||
              (attr.value !== undefined && attr.value !== null)
            ) {
              <div
                class="px-4 py-3 grid grid-cols-4 sm:grid-cols-2 sm:gap-4 border-stroke"
              >
                <dt
                  class="col-span-3 sm:col-span-1 text-sm font-medium leading-6 text-black me-4"
                >
                  {{ attr.title | translate }}
                </dt>
                <dd class="text-sm font-medium leading-6 text-black">
                  <app-variable-value
                    [value]="attr.value"
                    [uom]="state.isMobile ? undefined : attr.uom"
                    [alertType]="attr.alertType"
                  ></app-variable-value>
                </dd>
              </div>
            }
          }
        }
      </dl>
      <dl
        [formGroup]="form"
        class="divide-y divide-gray-100 w-full px-6 border-t border-stroke pt-6 lg:border-t-0 lg:pt-0 lg:border-l-2 lg:ps-5 lg:pe-2 lg:w-1/2"
      >
        <mat-form-field class="w-full">
          <mat-label>{{ "nodeName" | translate }}</mat-label>
          <input matInput formControlName="name" />
        </mat-form-field>

        @if (state.currentNode.nodeItem.cameraId) {
          <mat-form-field class="w-full !border-t-0">
            <mat-label>{{ "cameraName" | translate }}</mat-label>
            <input matInput formControlName="cameraName" />
          </mat-form-field>
        }

        <div
          class="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 !border-t-0"
        >
          <dt
            class="sm:col-span-2 text-sm font-medium leading-6 text-black me-4"
          >
            {{ "visibility" | translate }}
          </dt>
          <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-0">
            <mat-slide-toggle
              formControlName="visible"
              color="primary"
            ></mat-slide-toggle>
          </dd>
        </div>

        <div
          class="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 border-stroke"
        >
          <dt
            class="sm:col-span-2 text-sm font-medium leading-6 text-black me-4 flex items-center"
          >
            {{ "location" | translate }}
          </dt>
          <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-0">
            <mat-select formControlName="location">
              <mat-option value="inside">{{ "indoor" | translate }}</mat-option>
              <mat-option value="outside">{{
                "outdoor" | translate
              }}</mat-option>
            </mat-select>
          </dd>
        </div>

        <div
          class="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 border-stroke"
        >
          <dt
            class="sm:col-span-2 text-sm font-medium leading-6 text-black me-4 flex items-center"
          >
            {{ "nodesStatusWarningAlerts" | translate }}
          </dt>
          <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-0">
            <mat-slide-toggle
              formControlName="statusWarningAlerts"
              color="primary"
            ></mat-slide-toggle>
          </dd>
        </div>

        <div
          class="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 border-stroke"
        >
          <dt
            class="sm:col-span-2 text-sm font-medium leading-6 text-black me-4 flex items-center"
          >
            {{ "nodesOfflineAlerts" | translate }}
          </dt>
          <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-0">
            <mat-slide-toggle
              formControlName="offlineAlerts"
              color="primary"
            ></mat-slide-toggle>
          </dd>
        </div>

        <ng-container formGroupName="gauge">
          <div
            class="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 border-stroke"
          >
            <dt
              class="sm:col-span-2 text-sm font-medium leading-6 text-black me-4 flex items-center"
            >
              {{ "enableGauge" | translate }}
            </dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-0">
              <mat-slide-toggle
                formControlName="enabled"
                color="primary"
              ></mat-slide-toggle>
            </dd>
          </div>

          <div class="px-4 py-0 sm:px-0 border-none">
            <mat-form-field class="w-full">
              <mat-label>{{ "chooseGaugeVariable" | translate }}</mat-label>
              <mat-select formControlName="variableName">
                @for (variable of state.variables; track variable.name) {
                  @if (
                    !state.currentNode.nodeItem.allowedVariables ||
                    state.currentNode.nodeItem.allowedVariables.includes(
                      variable.id
                    )
                  ) {
                    <mat-option [value]="variable.name">
                      {{ variable.name | translate }}
                    </mat-option>
                  }
                }
              </mat-select>
            </mat-form-field>
          </div>

          <div class="px-4 py-0 grid grid-cols-2 gap-4 sm:px-0 border-none">
            <div>
              <mat-form-field class="w-full">
                <mat-label>{{ "min" | translate }}</mat-label>
                <input matInput type="number" formControlName="minValue" />
              </mat-form-field>
            </div>
            <div>
              <mat-form-field class="w-full">
                <mat-label>{{ "max" | translate }}</mat-label>
                <input matInput type="number" formControlName="maxValue" />
              </mat-form-field>
            </div>
          </div>
        </ng-container>
      </dl>
    </div>

    <div
      class="absolute right-0 top-0 h-full flex justify-center items-center pe-1 lg:pe-5"
    >
      <button mat-mini-fab (click)="scrollNodes(1)" color="light">
        <mat-icon>arrow_forward</mat-icon>
      </button>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
    <button mat-button mat-dialog-close>{{ "close" | translate }}</button>
    <button
      mat-button
      mat-dialog-close
      [routerLink]="['/admin/charts']"
      (click)="openCharts(state.currentNode.nodeItem)"
      class="!bg-indigo-50"
    >
      <mat-icon>bar_chart</mat-icon> {{ "charts" | translate }}
    </button>
  </mat-dialog-actions>
</ng-container>
