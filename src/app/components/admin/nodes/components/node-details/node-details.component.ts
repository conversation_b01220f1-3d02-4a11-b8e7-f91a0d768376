import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { RouterModule } from '@angular/router';
import { LetDirective } from '@ngrx/component';
import { concatLatestFrom } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { isNumber } from 'lodash';
import { Observable, filter, tap } from 'rxjs';
import { INodesData, IVariableToDisplay } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { INodeForm, INodeListItem } from '../../../../../model/node';
import { adminFeature } from '../../../../../state/admin/feature';
import { NodesActions } from '../../state/nodes.actions';
import { VariableValueComponent } from '../variable-value/variable-value.component';
import { nodesFeature } from './../../state/nodes.feature';

@Component({
  selector: 'app-node-details',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    VariableValueComponent,
    LetDirective,
  ],
  templateUrl: './node-details.component.html',
  styleUrl: './node-details.component.scss',
})
export class NodeDetailsComponent extends BaseComponent implements OnInit {
  variables$!: Observable<IVariableToDisplay[]>;
  currentNode$!: Observable<{
    nodeItem: INodeListItem;
    nodeData: INodesData | undefined;
  }>;

  form = new FormGroup(
    {
      name: new FormControl('', Validators.required),
      cameraName: new FormControl(),
      visible: new FormControl(),
      location: new FormControl(),
      statusWarningAlerts: new FormControl(),
      offlineAlerts: new FormControl(),
      gauge: new FormGroup({
        enabled: new FormControl(),
        variableName: new FormControl(),
        minValue: new FormControl(),
        maxValue: new FormControl(),
      }),
    },
    { updateOn: 'blur' },
  );

  private currentNodeId!: number;

  constructor(
    @Inject(MAT_DIALOG_DATA) private data: INodeListItem,
    private store: Store,
  ) {
    super();
  }

  validateGaugeValues(form: INodeForm): boolean {
    if (!form.gauge?.enabled) return true;

    const { minValue, maxValue } = form.gauge;
    if (
      minValue !== undefined &&
      maxValue !== undefined &&
      minValue >= maxValue
    ) {
      this.ui.showMessage('Minimum value must be smaller than maximum value');
      return false;
    }
    return true;
  }

  ngOnInit() {
    this.variables$ = this.store.select(adminFeature.selectVariables);
    this.currentNode$ = this.store
      .select(nodesFeature.selectCurrentScrollingNode)
      .pipe(
        filter((node) => !!node.nodeData),
        tap((node) => {
          this.currentNodeId = node!.nodeData!.remote_id;
          this.form.setValue(
            {
              name: node!.nodeData!.node_name,
              cameraName: node!.nodeItem.cameraName ?? '',
              visible: !!node!.nodeData!.visible,
              location: node!.nodeData!.physical_location,
              statusWarningAlerts:
                node!.nodeData!.node_alerts.nodes_status_warning_alerts,
              offlineAlerts: node!.nodeData!.node_alerts.nodes_offline_alerts,
              gauge: {
                enabled: node!.nodeData!.node_gauges?.[0]?.['enabled'] ?? false,
                variableName:
                  node!.nodeData!.node_gauges?.[0]?.variable_name ?? '',
                minValue: node!.nodeData!.node_gauges?.[0]?.min_value ?? 0,
                maxValue: node!.nodeData!.node_gauges?.[0]?.max_value ?? 0,
              },
            },
            { emitEvent: false },
          );
        }),
      );

    this.subSafe(
      this.form.controls.gauge.controls.variableName.valueChanges.pipe(
        concatLatestFrom(() => this.store.select(adminFeature.selectVariables)),
      ),
      (value: [string, IVariableToDisplay[]]) => {
        const [variableName, variables] = value;
        const variable = variables.find((v) => v.name === variableName);

        if (!variable) {
          return;
        }

        this.form.patchValue({
          gauge: {
            minValue: variable.min ?? 0,
            maxValue: variable.max ?? 0,
          },
        });
      },
    );

    this.subSafe(this.form.valueChanges, (form: INodeForm) => {
      if (!this.form.valid) {
        this.ui.showMessage('Please enter device name');
        return;
      }

      if (
        form.gauge?.enabled &&
        (!form.gauge?.variableName ||
          !isNumber(form.gauge?.minValue) ||
          !isNumber(form.gauge?.maxValue))
      ) {
        return;
      }

      if (!this.validateGaugeValues(form)) {
        return;
      }

      this.store.dispatch(
        NodesActions.nodeUpdated({ nodeId: this.currentNodeId, form }),
      );
    });

    this.store.dispatch(
      NodesActions.startActiveNodeScrolling({ nodeId: this.data.id }),
    );
  }

  scrollNodes(delta: number) {
    this.store.dispatch(NodesActions.scrollActiveNode({ delta }));
  }

  openCharts(nodeItem: INodeListItem) {
    this.store.dispatch(NodesActions.openChartsForNode({ node: nodeItem }));
  }
}
