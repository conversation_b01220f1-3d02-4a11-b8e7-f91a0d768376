import { Component, OnInit } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { AgGridModule } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { Observable } from 'rxjs';
import { INodesData } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { selectNodesDataWithLocalTime } from '../../../../../state/selectors';
import { NodesActions } from '../../state/nodes.actions';
import { nodesFeature } from '../../state/nodes.feature';

@Component({
  selector: 'app-readings',
  standalone: true,
  imports: [...SharedModules, AgGridModule, LetDirective],
  templateUrl: './readings.component.html',
  styleUrl: './readings.component.scss',
})
export class ReadingsComponent extends BaseComponent implements OnInit {
  nodesData$!: Observable<INodesData[]>;
  columns$!: Observable<ColDef[]>;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.nodesData$ = this.store.select(selectNodesDataWithLocalTime);
    this.columns$ = this.store.select(nodesFeature.selectReadingColumns);
  }

  refresh() {
    this.store.dispatch(NodesActions.updateNodesData());
  }
}
