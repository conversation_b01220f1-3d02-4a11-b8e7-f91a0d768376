<ng-container *ngrxLet="nodesData$ as nodesData">
  <ng-container *ngrxLet="columns$ as columns">
    <div class="w-full h-full p-6">
      <ag-grid-angular
        [rowData]="nodesData"
        [columnDefs]="columns"
        class="ag-theme-material h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
      >
      </ag-grid-angular>
    </div>
  </ng-container>
</ng-container>

<div class="fabs !fixed z-10 right-5 bottom-5 flex flex-col">
  <button mat-mini-fab color="light" (click)="refresh()" class="mb-2">
    <mat-icon>refresh</mat-icon>
  </button>
</div>
