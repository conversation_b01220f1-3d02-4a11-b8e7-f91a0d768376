import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { merge } from 'lodash';
import { IVisualMapGetResponseItem } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { CameraPointDialogComponent } from '../../../dashboard/components/camera-point-dialog/camera-point-dialog.component';

@Component({
  selector: 'app-visual-map-details',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './visual-map-details.component.html',
  styleUrl: './visual-map-details.component.scss',
})
export class VisualMapDetailsComponent extends BaseComponent implements OnInit {
  form = new FormGroup({
    name: new FormControl<string | undefined>(undefined, {
      nonNullable: true,
      validators: [Validators.required],
    }),
  });

  constructor(
    private dialogRef: MatDialogRef<CameraPointDialogComponent>,
    @Inject(MAT_DIALOG_DATA) private visualMap: IVisualMapGetResponseItem,
  ) {
    super();
  }

  ngOnInit() {
    this.form.reset(this.visualMap);
  }

  confirm() {
    this.dialogRef.close(merge(this.visualMap, this.form.getRawValue()));
  }
}
