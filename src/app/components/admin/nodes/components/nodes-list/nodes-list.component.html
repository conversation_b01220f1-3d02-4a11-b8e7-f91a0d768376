<ng-container *ngrxLet="activeLocation$ as activeLocation">
  @if (activeLocation?.configuration) {
    <ng-container *ngrxLet="ui.isMobile$ as isMobile">
      <mat-drawer-container class="w-full h-full">
        <mat-drawer
          #nodesDrawer
          mode="over"
          position="end"
          class="w-80 max-w-[90%] !bg-white"
        >
          <div
            cdkDropList
            (cdkDropListDropped)="drop($event)"
            class="w-full h-full p-4"
          >
            @for (nodeItem of orderedNodeList; track nodeItem) {
              <div cdkDrag class="w-full mb-4 bg-white">
                <div
                  class="cursor-pointer rounded-sm border border-stroke bg-white shadow-default px-4 py-3"
                >
                  <h4 class="font-medium">
                    {{ nodeItem.name }}
                  </h4>
                </div>
              </div>
            }
          </div>
        </mat-drawer>
        <mat-drawer-content>
          <ng-container
            *ngrxLet="{
              nodeList: nodeList$,
              isGaugeEnabled: isGaugeEnabled$,
              visibleAttrsCount: visibleAttrsCount$,
              variableStats: variableStats$
            } as nodesData"
          >
            <div class="p-4 ps-3 pe-3">
              <mat-grid-list
                [cols]="
                  isMobile ? 1 : activeLocation?.configuration?.nodemapx ?? 3
                "
                [rowHeight]="
                  (isMobile ? 134 : 164) +
                  (nodesData.isGaugeEnabled ? 140 : 0) +
                  nodesData.visibleAttrsCount * (isMobile ? 76 : 48)
                "
              >
                @for (nodeItem of nodesData.nodeList; track nodeItem) {
                  <mat-grid-tile>
                    <div
                      class="w-full h-full px-3"
                      [ngClass]="{ 'opacity-30': !nodeItem.visible }"
                    >
                      <div class="h-7 flex justify-start items-center">
                        <ng-container
                          *ngTemplateOutlet="
                            lastSeenTmpl;
                            context: {
                              date: nodeItem.date,
                              hours: nodeItem.date | dpFromNow: 'hours'
                            }
                          "
                        ></ng-container>
                        @if (nodeItem.lastReading) {
                          <ng-container
                            *ngTemplateOutlet="
                              lastSeenTmpl;
                              context: {
                                date: nodeItem.lastReading,
                                hours:
                                  nodeItem.lastReading | dpFromNow: 'hours',
                                icon: 'camera'
                              }
                            "
                          ></ng-container>
                        }
                      </div>
                      <div
                        (click)="openNodeDetails(nodeItem)"
                        class="cursor-pointer rounded-sm border border-stroke bg-white p-6 !pb-3 shadow-default dark:border-strokedark dark:bg-boxdark"
                      >
                        <div class="flex items-end justify-between">
                          <div class="w-full relative">
                            <h4 class="h-8 mb-2 font-medium flex items-center">
                              @for (
                                alertType of nodeItem.alerts;
                                track alertType
                              ) {
                                <ng-container
                                  *ngTemplateOutlet="
                                    alertTmpl;
                                    context: { alertType: alertType }
                                  "
                                ></ng-container>
                              }
                              @if (nodeItem.cameraId) {
                                <mat-icon class="me-1.5 text-neutral-600"
                                  >camera</mat-icon
                                >
                                @if (nodeItem.cameraName) {
                                  {{ nodeItem.cameraName }}
                                  ({{ nodeItem.cameraId }}) /
                                } @else {
                                  {{ "camera" | translate }}
                                  {{ nodeItem.cameraId }} /
                                }
                              }
                              {{ nodeItem.id }}
                            </h4>
                            <h3
                              [matTooltip]="nodeItem.name"
                              class="text-title-md font-bold text-black dark:text-white line-clamp-1"
                            >
                              {{ nodeItem.name }}
                            </h3>

                            @if (nodeItem.gauge?.enabled) {
                              @if (
                                nodeItem.gauge?.alertType !== alertTypes.unknown
                              ) {
                                <div
                                  [ngClass]="{
                                    'bg-[#EF5350]':
                                      nodeItem.gauge?.alertType ===
                                      alertTypes.high,
                                    'bg-[#0091db]':
                                      nodeItem.gauge?.alertType ===
                                      alertTypes.low
                                  }"
                                  class="absolute top-[-15px] right-[-15px] w-8 h-8 rounded-full flex items-center justify-center"
                                >
                                  <mat-icon class="text-white"
                                    >swap_driving_apps_wheel</mat-icon
                                  >
                                </div>
                              }

                              <ng-container
                                *ngTemplateOutlet="
                                  gaugeTmpl;
                                  context: {
                                    attrs: nodeItem.attributes,
                                    variable: nodeItem.gauge!.variableName,
                                    rangeMin:
                                      nodesData.variableStats[
                                        nodeItem.gauge!.variableName
                                      ].minValue,
                                    rangeMax:
                                      nodesData.variableStats[
                                        nodeItem.gauge!.variableName
                                      ].maxValue,
                                    valueMin: nodeItem.gauge!.min,
                                    valueMax: nodeItem.gauge!.max
                                  }
                                "
                              ></ng-container>
                            }

                            @if (nodeItem.attributes.length) {
                              <dl class="mt-2 divide-y divide-gray-100">
                                @for (
                                  attr of nodeItem.attributes;
                                  track attr.id
                                ) {
                                  @if (attr.selected) {
                                    <ng-container
                                      *ngTemplateOutlet="
                                        nodeAttrTmpl;
                                        context: { attr }
                                      "
                                    ></ng-container>
                                  }
                                }
                              </dl>
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
              </mat-grid-list>
            </div>
          </ng-container>
        </mat-drawer-content>
      </mat-drawer-container>
      <div class="fabs !fixed z-10 right-5 bottom-5 flex flex-col">
        <button
          mat-mini-fab
          color="light"
          [matMenuTriggerFor]="sortMenu"
          [matTooltip]="'sort' | translate"
          matTooltipPosition="left"
          class="mb-2"
        >
          <mat-icon>swap_vert</mat-icon>
        </button>
        <mat-menu #sortMenu="matMenu">
          <button mat-menu-item (click)="setSortedNodeList('name')">
            {{ "nodeName" | translate }}
          </button>
          <button mat-menu-item (click)="setSortedNodeList('id')">
            {{ "remoteIDdevice" | translate }}
          </button>
          <button mat-menu-item (click)="setSortedNodeList('date', 'desc')">
            {{ "lastMessage" | translate }}
          </button>
          <button
            mat-menu-item
            (click)="setSortedNodeList('custom'); nodesDrawer.toggle()"
          >
            {{ "custom" | translate }}
          </button>
        </mat-menu>
        @if (!isMobile) {
          <app-download-button
            (openDownloadDialog)="openDownloadDialog()"
            class="mb-2"
          ></app-download-button>
        }
      </div>
    </ng-container>
  }
</ng-container>

<ng-template #lastSeenTmpl let-date="date" let-hours="hours" let-icon="icon">
  <div
    [ngClass]="{
      'bg-green-100 text-green-800': hours <= 1,
      'bg-yellow-100 text-yellow-800': hours > 1 && hours <= 6,
      'bg-rose-100 text-rose-800': hours > 6
    }"
    class="flex justify-start items-center text-xs font-medium px-2.5 py-0.5 me-1 rounded"
  >
    @if (icon) {
      <mat-icon class="me-1 text-sm !w-auto !h-auto">{{ icon }}</mat-icon>
    }
    {{ date | dpTimeAgo | async }}
  </div>
</ng-template>

<ng-template #nodeAttrTmpl let-attr="attr">
  <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0 border-stroke">
    <dt class="text-sm leading-6 whitespace-nowrap">
      {{ attr.id | translate }}
    </dt>
    <dd class="mt-1 text-base leading-6 sm:col-span-2 sm:mt-0 text-right">
      <app-variable-value
        [value]="attr.value"
        [uom]="attr.uom"
        [alertType]="attr.alertType"
      ></app-variable-value>
    </dd>
  </div>
</ng-template>

<!-- TODO: create component for alert and share with parent nodes component -->
<ng-template #alertTmpl let-alertType="alertType">
  @switch (alertType) {
    @case (alertTypes.high) {
      <mat-icon class="bg-pink-100 text-pink-800 rounded me-2">
        expand_less
      </mat-icon>
    }
    @case (alertTypes.low) {
      <mat-icon class="bg-indigo-100 text-indigo-800 rounded me-2">
        expand_more
      </mat-icon>
    }
  }
</ng-template>

<ng-template
  #gaugeTmpl
  let-attrs="attrs"
  let-variable="variable"
  let-valueMin="valueMin"
  let-valueMax="valueMax"
  let-rangeMin="rangeMin"
  let-rangeMax="rangeMax"
  let-uom="uom"
>
  <app-gauge
    [variable]="variable"
    [value]="+((attrs | nodeAttribute: variable)?.value ?? 0)"
    [valueMin]="valueMin"
    [valueMax]="valueMax"
    [rangeMin]="valueMin * 0.8"
    [rangeMax]="valueMax * 1.2"
    [uom]="(attrs | nodeAttribute: variable)?.uom"
  ></app-gauge>
</ng-template>
