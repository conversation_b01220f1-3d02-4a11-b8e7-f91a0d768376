import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatGridListModule } from '@angular/material/grid-list';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import { Observable, map, tap } from 'rxjs';
import { IVariableToDisplay } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { DpFromNowPipe } from '../../../../../common/pipes/dayjs/dp-from-now.pipe';
import { DpTimeAgoPipe } from '../../../../../common/pipes/dayjs/dp-time-ago.pipe';
import { NodeAttributePipe } from '../../../../../common/pipes/node-attribute.pipe';
import SharedModules from '../../../../../common/shared.modules';
import { ISigrowLocation } from '../../../../../model/admin';
import { AlertType } from '../../../../../model/alert';
import { IConfiguredDevice } from '../../../../../model/dashboard';
import { INodeListItem, NodeAttributeType } from '../../../../../model/node';
import { adminFeature } from '../../../../../state/admin/feature';
import { DownloadButtonComponent } from '../../../../common/download-button/download-button.component';
import {
  DownloadDialogComponent,
  IDownloadConfig,
} from '../../../../common/download-dialog/download-dialog.component';
import { GaugeComponent } from '../../../../common/gauge/gauge.component';
import { NodesActions } from '../../state/nodes.actions';
import { nodesFeature } from '../../state/nodes.feature';
import { NodeDetailsComponent } from '../node-details/node-details.component';
import { VariableValueComponent } from '../variable-value/variable-value.component';

@Component({
  selector: 'app-nodes-list',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    CdkDropList,
    CdkDrag,
    MatGridListModule,
    VariableValueComponent,
    DownloadButtonComponent,
    GaugeComponent,
    LetDirective,
    DpTimeAgoPipe,
    DpFromNowPipe,
    NodeAttributePipe,
  ],
  templateUrl: './nodes-list.component.html',
  styleUrl: './nodes-list.component.scss',
})
export class NodesListComponent extends BaseComponent implements OnInit {
  constructor(
    private store: Store,
    private dialog: MatDialog,
  ) {
    super();
  }
  activeLocation$!: Observable<ISigrowLocation | undefined>;
  nodeList$!: Observable<INodeListItem[]>;
  isGaugeEnabled$!: Observable<boolean>;
  visibleAttrsCount$!: Observable<number>;
  orderedNodeList!: INodeListItem[];
  variableStats$!: Observable<{
    [variable: string]: { minValue: number; maxValue: number };
  }>;

  alertTypes = AlertType;

  ngOnInit() {
    this.activeLocation$ = this.store.select(adminFeature.selectActiveLocation);

    this.nodeList$ = this.store
      .select(nodesFeature.selectSortedNodesList)
      .pipe(tap((list) => (this.orderedNodeList = list)));

    this.isGaugeEnabled$ = this.store.select(nodesFeature.selectIsGaugeEnabled);

    this.variableStats$ = this.nodeList$.pipe(
      map((nodes) => this.calculateVariableStats(nodes)),
    );

    this.visibleAttrsCount$ = this.store.select(
      nodesFeature.selectVisibleAttrsCount,
    );
  }

  openNodeDetails(nodeItem: INodeListItem) {
    this.dialog.open(NodeDetailsComponent, {
      data: nodeItem,
    });
  }

  drop(event: CdkDragDrop<INodeListItem[]>) {
    moveItemInArray(
      this.orderedNodeList,
      event.previousIndex,
      event.currentIndex,
    );
    this.store.dispatch(
      NodesActions.customNodesOrderUpdated({
        nodesOrder: this.orderedNodeList.map((n) => n.id),
      }),
    );
  }

  openDownloadDialog() {
    this.dialog.open(DownloadDialogComponent, {
      data: {
        startDate: dayjs().subtract(1, 'week').toDate(),
        endDate: dayjs().toDate(),
        preSelectedDevices: this.getPreSelectedDevicesForDownload(),
        preSelectedVariables: this.getPreselectedVariablesForDownload(),
      } satisfies IDownloadConfig,
    });
  }

  setSortedNodeList(fieldName: string, direction: 'asc' | 'desc' = 'asc') {
    this.store.dispatch(
      NodesActions.setNodesSortField({
        sortField: { field: fieldName, direction },
      }),
    );
  }

  private getPreSelectedDevicesForDownload() {
    return this.store.select(nodesFeature.selectSortedNodesList).pipe(
      map((nodes) =>
        nodes.map(
          (node) =>
            ({
              name: node.name,
              remote_id: node.id,
              thermal_camera_id: node.cameraId,
            }) satisfies Partial<IConfiguredDevice>,
        ),
      ),
    );
  }

  private getPreselectedVariablesForDownload() {
    return this.store.select(nodesFeature.selectAttrsWithAlerts).pipe(
      map((groups) =>
        groups
          .filter((g) => g.type !== NodeAttributeType.attribute)
          .map((group) =>
            group.attributes
              .filter((attr) => attr.selected)
              .map(
                (attr) =>
                  ({
                    name: attr.id,
                    full_name: attr.title,
                  }) satisfies Partial<IVariableToDisplay>,
              )
              .flat(),
          )
          .flat(),
      ),
    );
  }

  private calculateVariableStats(nodes: INodeListItem[]): {
    [variable: string]: { minValue: number; maxValue: number };
  } {
    const variableMap = new Map<string, { min: number; max: number }>();

    // Process all nodes
    for (const node of nodes) {
      // Process gauge configuration if present
      if (node.gauge?.enabled && node.gauge.variableName) {
        const stats = variableMap.get(node.gauge.variableName) || {
          min: Infinity,
          max: -Infinity,
        };

        if (typeof node.gauge.min === 'number') {
          stats.min = Math.min(stats.min, node.gauge.min);
        }
        if (typeof node.gauge.max === 'number') {
          stats.max = Math.max(stats.max, node.gauge.max);
        }

        variableMap.set(node.gauge.variableName, stats);
      }

      // Process all node attributes
      for (const attr of node.attributes) {
        if (typeof attr.value === 'number') {
          const stats = variableMap.get(attr.id) || {
            min: Infinity,
            max: -Infinity,
          };

          stats.min = Math.min(stats.min, attr.value);
          stats.max = Math.max(stats.max, attr.value);

          variableMap.set(attr.id, stats);
        }
      }
    }

    const result: {
      [variable: string]: { minValue: number; maxValue: number };
    } = {};

    for (const [variable, stats] of variableMap.entries()) {
      if (stats.min !== Infinity && stats.max !== -Infinity) {
        result[variable] = {
          minValue: stats.min,
          maxValue: stats.max,
        };
      }
    }

    return result;
  }
}
