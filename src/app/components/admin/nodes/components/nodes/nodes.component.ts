import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable, lastValueFrom } from 'rxjs';
import {
  IVisualMapGetResponseItem,
  VisualMapGetResponseItem,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { AlertType } from '../../../../../model/alert';
import {
  INodeAttribute,
  INodeAttributesGroupWithAlert,
  NodeAttributeType,
} from '../../../../../model/node';
import { VariableUom } from '../../../../../model/variable';
import { MobileSidenavMenuBtnComponent } from '../../../../common/mobile-sidenav-menu-btn/mobile-sidenav-menu-btn.component';
import { HeatmapComponent } from '../../../heatmap/components/heatmap/heatmap.component';
import { NodesActions } from '../../state/nodes.actions';
import { UiMode, nodesFeature } from '../../state/nodes.feature';
import { NodesListComponent } from '../nodes-list/nodes-list.component';
import { VisualMapDetailsComponent } from '../visual-map-details/visual-map-details.component';
import { VisualMapComponent } from '../visual-map/visual-map.component';

@Component({
  selector: 'app-nodes',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    NodesListComponent,
    VisualMapComponent,
    HeatmapComponent,
    MobileSidenavMenuBtnComponent,
    LetDirective,
  ],
  templateUrl: './nodes.component.html',
  styleUrl: './nodes.component.scss',
})
export class NodesComponent extends BaseComponent implements OnInit {
  attrGroups$!: Observable<INodeAttributesGroupWithAlert[]>;
  uiMode$!: Observable<UiMode>;
  visualMaps$!: Observable<IVisualMapGetResponseItem[]>;

  types = NodeAttributeType;
  alertTypes = AlertType;
  uoms = VariableUom;
  uiModes = UiMode;

  constructor(
    private store: Store,
    private dialog: MatDialog,
  ) {
    super();
  }

  ngOnInit() {
    this.attrGroups$ = this.store.select(nodesFeature.selectAttrsWithAlerts);
    this.uiMode$ = this.store.select(nodesFeature.selectUiMode);
    this.visualMaps$ = this.store.select(nodesFeature.selectVisualMaps);
  }

  toggleAttribute(attribute: INodeAttribute) {
    this.store.dispatch(NodesActions.toggleAttribute({ id: attribute.id }));
  }

  setUiMode(uiMode: UiMode) {
    this.store.dispatch(NodesActions.setUiMode({ uiMode }));
  }

  async addVisualMap() {
    const dialogRef = this.dialog.open(VisualMapDetailsComponent, {
      data: new VisualMapGetResponseItem(),
    });

    const res: IVisualMapGetResponseItem | undefined = await lastValueFrom(
      dialogRef.afterClosed(),
    );

    if (!res) {
      return;
    }

    this.store.dispatch(NodesActions.createVisualMap({ name: res.name }));
  }

  openVisualMap(visualMap: IVisualMapGetResponseItem) {
    this.store.dispatch(NodesActions.openVisualMap({ visualMap }));
  }
}
