<ng-container *ngrxLet="ui.isMobile$ as isMobile">
  <ng-container *ngrxLet="uiMode$ as uiMode">
    <app-mobile-sidenav-menu-btn
      [sidenav]="sidenav"
    ></app-mobile-sidenav-menu-btn>
    <mat-sidenav-container class="h-full">
      <mat-sidenav
        #sidenav
        [mode]="isMobile ? 'over' : 'side'"
        [opened]="!isMobile"
        class="sidenav w-70 !bg-black !dark:bg-boxdark"
      >
        <mat-nav-list>
          <a
            mat-list-item
            [ngClass]="{ '!bg-graydark': uiMode === uiModes.nodes }"
            (click)="setUiMode(uiModes.nodes)"
          >
            <div class="flex text-white ps-2">
              <mat-icon class="me-3">grid_view</mat-icon>
              <span>{{ "nodes" | translate }}</span>
              @if (uiMode === uiModes.nodes) {
                <span class="flex-1"></span>
                <mat-icon>chevron_right</mat-icon>
              }
            </div>
          </a>

          <a
            mat-list-item
            [matMenuTriggerFor]="visualMapsMenu"
            [ngClass]="{ '!bg-graydark': uiMode === uiModes.visualMap }"
          >
            <div class="flex text-white ps-2">
              <mat-icon class="me-3">location_on</mat-icon>
              <span>{{ "visualMap" | translate }}</span>
              @if (uiMode === uiModes.visualMap) {
                <span class="flex-1"></span>
                <mat-icon>chevron_right</mat-icon>
              }
            </div>
          </a>

          <!-- <a
            mat-list-item
            [ngClass]="{ '!bg-graydark': uiMode === uiModes.heatMap }"
            (click)="setUiMode(uiModes.heatMap)"
          >
            <div class="flex text-white ps-2">
              <mat-icon class="me-3">thermostat</mat-icon>
              <span>{{ "heatmap" | translate }}</span>
              @if (uiMode === uiModes.heatMap) {
                <span class="flex-1"></span>
                <mat-icon>chevron_right</mat-icon>
              }
            </div>
          </a> -->

          <mat-menu
            #visualMapsMenu="matMenu"
            overlapTrigger="true"
            class="!absolute left-60 top-2"
          >
            <button mat-menu-item (click)="addVisualMap()">
              <mat-icon>add</mat-icon>
              <span>Add new visual map</span>
            </button>
            <ng-container *ngrxLet="visualMaps$ as visualMaps">
              @if (visualMaps.length) {
                <mat-divider></mat-divider>
              }
              @for (visualMap of visualMaps; track visualMap) {
                <button mat-menu-item (click)="openVisualMap(visualMap)">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ visualMap.name }}</span>
                </button>
              }
            </ng-container>
          </mat-menu>
        </mat-nav-list>

        <mat-divider class="!border-t-white"></mat-divider>

        <div class="p-4">
          <ng-container *ngrxLet="attrGroups$ as attrGroups">
            @for (attrGroup of attrGroups; track attrGroup.type) {
              @if (attrGroup.type !== types.attribute) {
                @for (attr of attrGroup.attributes; track attr.id) {
                  <div class="relative">
                    @if (attr.selected) {
                      <button
                        mat-raised-button
                        color="primary"
                        (click)="toggleAttribute(attr)"
                        class="w-full !justify-start mb-2 !h-auto !text-left !py-2 !text-bodydark1 !border-solid !border !border-[#0091db] !tracking-wider"
                      >
                        {{ attr.id | translate }}
                      </button>
                    } @else {
                      <button
                        mat-stroked-button
                        (click)="toggleAttribute(attr)"
                        class="w-full !font-medium !justify-start mb-2 !h-auto !text-left !py-2 !border-black !text-bodydark1 hover:!bg-graydark dark:hover:!bg-meta-4 hover:!border-gray !tracking-wider"
                      >
                        {{ attr.id | translate }}
                      </button>
                    }
                    @for (
                      alertType of attr.alertTypes;
                      track alertType;
                      let idx = $index
                    ) {
                      <div
                        class="absolute z-10 top-[9px]"
                        [style.right.px]="2 + 30 * idx"
                      >
                        <ng-container
                          *ngTemplateOutlet="
                            alertTmpl;
                            context: { alertType: alertType }
                          "
                        ></ng-container>
                      </div>
                    }
                  </div>
                }
              }
            }
          </ng-container>
        </div>
      </mat-sidenav>
      <mat-sidenav-content>
        @switch (uiMode) {
          @case (uiModes.nodes) {
            <app-nodes-list></app-nodes-list>
          }
          @case (uiModes.visualMap) {
            <app-visual-map></app-visual-map>
          }
          @case (uiModes.heatMap) {
            <app-heatmap></app-heatmap>
          }
        }
      </mat-sidenav-content>
    </mat-sidenav-container>
  </ng-container>
</ng-container>

<ng-template #alertTmpl let-alertType="alertType">
  @switch (alertType) {
    @case (alertTypes.high) {
      <mat-icon class="bg-pink-100 text-pink-800 rounded me-2">
        expand_less
      </mat-icon>
    }
    @case (alertTypes.low) {
      <mat-icon class="bg-indigo-100 text-indigo-800 rounded me-2">
        expand_more
      </mat-icon>
    }
  }
</ng-template>
