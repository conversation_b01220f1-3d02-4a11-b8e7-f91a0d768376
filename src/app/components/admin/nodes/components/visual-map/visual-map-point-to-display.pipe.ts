import { ElementRef, Pipe, PipeTransform } from '@angular/core';
import { IVisualMapPoint } from '../../../../../model/node';
import { VisualMapUtils } from '../../../../../utils/visualMap';

@Pipe({
  name: 'visualMapPointToDisplay',
  standalone: true,
})
export class VisualMapPointToDisplayPipe implements PipeTransform {
  transform(
    point: IVisualMapPoint | undefined,
    image: ElementRef<HTMLImageElement> | undefined,
  ) {
    return point && image
      ? VisualMapUtils.convertCoordsToDisplay(point, image)
      : { x: 0, y: 0 };
  }
}
