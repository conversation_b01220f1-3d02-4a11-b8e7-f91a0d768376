import { CdkDrag, CdkDragEnd } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { cloneDeep, merge } from 'lodash';
import { Observable, map, tap } from 'rxjs';
import { IVisualMapGetResponseItem } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { IDevice } from '../../../../../model/dashboard';
import {
  INodeAttributeValue,
  IVisualMapData,
  IVisualMapDevice,
} from '../../../../../model/node';
import { selectVisualMapDevices } from '../../../../../state/selectors';
import { VisualMapUtils } from '../../../../../utils/visualMap';
import { NodesActions } from '../../state/nodes.actions';
import { nodesFeature } from '../../state/nodes.feature';
import { VariableValueComponent } from '../variable-value/variable-value.component';
import { VisualMapDeviceNamePipe } from './visual-map-device-name.pipe';
import { VisualMapPointToDisplayPipe } from './visual-map-point-to-display.pipe';
import { VisualMapReadingsPipe } from './visual-map-readings.pipe';

@Component({
  selector: 'app-visual-map',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    CdkDrag,
    VariableValueComponent,
    LetDirective,
    VisualMapReadingsPipe,
    VisualMapPointToDisplayPipe,
    VisualMapDeviceNamePipe,
  ],
  templateUrl: './visual-map.component.html',
  styleUrl: './visual-map.component.scss',
})
export class VisualMapComponent extends BaseComponent implements OnInit {
  @ViewChild('vmImage') vmImage!: ElementRef<HTMLImageElement>;

  visualMap$!: Observable<IVisualMapGetResponseItem | undefined>;
  selectedDeviceAttrs$!: Observable<INodeAttributeValue[] | undefined>;
  selectedDeviceId$!: Observable<number | undefined>;
  connectedDevices$?: Observable<IVisualMapDevice[]>;
  disconnectedDevices$?: Observable<IVisualMapDevice[]>;

  private currentMap?: IVisualMapGetResponseItem;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.visualMap$ = this.store
      .select(nodesFeature.selectActiveVisualMap)
      .pipe(tap((map) => (this.currentMap = map)));
    this.selectedDeviceAttrs$ = this.store.select(
      nodesFeature.selectVisualMapSelectedDeviceAttrs,
    );
    this.selectedDeviceId$ = this.store.select(
      nodesFeature.selectVisualMapSelectedDeviceId,
    );
  }

  onImageSelected(event: any) {
    const file: File = event.target.files[0];

    if (!file || !this.currentMap) {
      return;
    }

    if (file.size > 20 * 1024 * 1024) {
      this.ui.showErrorMessage(
        'Image file size is too big. Maximum allowed file size is 20 Mb.',
      );
      return;
    }

    this.store.dispatch(
      NodesActions.updateVisualMap({
        id: this.currentMap.id,
        name: this.currentMap.name,
        data: '',
        image: { fileName: file.name, data: file },
      }),
    );
  }

  onImageLoaded() {
    this.connectedDevices$ = undefined;
    this.disconnectedDevices$ = undefined;
    setTimeout(() => {
      this.connectedDevices$ = this.store
        .select(selectVisualMapDevices)
        .pipe(map((devices) => devices.filter((d) => !!d.position)));
      this.disconnectedDevices$ = this.store
        .select(selectVisualMapDevices)
        .pipe(map((devices) => devices.filter((d) => !d.position)));
    });
  }

  deviceDragEnded(device: IDevice, event: CdkDragEnd) {
    const data: IVisualMapData = cloneDeep(
      this.currentMap!.data,
    ) as unknown as IVisualMapData;

    data.devices ??= [];

    let mapDevice = data.devices.find((d) => d.id === device.remote_id);
    if (!mapDevice) {
      mapDevice = {
        id: device.remote_id,
        x: 0,
        y: 0,
      };
      data.devices.push(mapDevice);
    }

    const deviceRect =
      event.source.element.nativeElement.getBoundingClientRect();

    mapDevice = merge(
      mapDevice,
      VisualMapUtils.convertCoordsToStorage(
        { x: deviceRect.x, y: deviceRect.y },
        this.vmImage,
      ),
    );

    this.store.dispatch(
      NodesActions.updateVisualMap({
        id: this.currentMap!.id,
        data: JSON.stringify(data),
      }),
    );
  }

  selectDevice(device: IDevice) {
    this.store.dispatch(
      NodesActions.selectVisualMapDevice({ deviceId: device.remote_id }),
    );
  }

  async deleteVisualMap() {
    if (!(await this.ui.confirm())) {
      return;
    }
    this.store.dispatch(
      NodesActions.deleteVisualMap({ id: this.currentMap!.id }),
    );
  }
}
