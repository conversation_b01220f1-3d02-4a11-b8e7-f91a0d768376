import { Pipe, PipeTransform } from '@angular/core';
import { IDevice } from '../../../../../model/dashboard';

@Pipe({
  name: 'visualMapDeviceName',
  standalone: true,
})
export class VisualMapDeviceNamePipe implements PipeTransform {
  transform(device: IDevice, maxLength: number): string {
    const fullName = device.name || device.remote_id.toString();
    return fullName.length > maxLength
      ? `${fullName.slice(0, maxLength - 1)}...${fullName[fullName.length - 1]}`
      : fullName;
  }
}
