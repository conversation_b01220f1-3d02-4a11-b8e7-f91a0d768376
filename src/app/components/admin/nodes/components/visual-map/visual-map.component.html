<mat-drawer-container class="w-full h-full">
  <mat-drawer
    [opened]="true"
    mode="side"
    position="end"
    class="w-80 max-w-[90%] !bg-white"
  >
    <div class="w-full h-full">
      <ng-container *ngrxLet="selectedDeviceAttrs$ as attrs">
        @if (attrs?.length) {
          <mat-divider></mat-divider>
          @for (attr of attrs; let last = $last; track attr.id) {
            <div
              [ngClass]="{ 'pb-25': last }"
              class="px-4 py-3 grid grid-cols-4 sm:grid-cols-2 sm:gap-4 border-stroke"
            >
              <dt
                class="col-span-3 sm:col-span-1 text-sm font-medium leading-6 text-black me-4"
              >
                {{ attr.title }}
              </dt>
              <dd class="text-sm font-medium leading-6 text-black">
                <app-variable-value
                  [value]="attr.value"
                  [uom]="attr.uom"
                  [alertType]="attr.alertType"
                ></app-variable-value>
              </dd>
            </div>
            @if (!last) {
              <mat-divider></mat-divider>
            }
          }
        } @else {
          <div
            class="w-full h-full flex items-center justify-center text-xl text-neutral-500 p-8 text-center"
          >
            Please select device to get reading details
          </div>
        }
      </ng-container>
    </div>
  </mat-drawer>

  <mat-drawer-content>
    <ng-container *ngrxLet="visualMap$ as visualMap">
      @if (visualMap) {
        <div class="w-full h-full flex flex-col items-center justify-start">
          <h2
            class="mt-7 mb-4 text-2xl font-bold leading-none tracking-tight text-gray-900"
          >
            {{ visualMap.name }}
          </h2>
          @if (visualMap.image_url) {
            <div class="w-full h-full p-4">
              <ng-container *ngrxLet="selectedDeviceId$ as selectedDeviceId">
                <div cdkDropList class="w-full map-boundary relative">
                  <div class="absolute top-0 left-0">
                    <ng-container
                      *ngTemplateOutlet="
                        deviceListTmpl;
                        context: {
                          devices$: connectedDevices$,
                          connected: true,
                          selectedDeviceId: selectedDeviceId
                        }
                      "
                    ></ng-container>
                  </div>
                  <img
                    #vmImage
                    [src]="visualMap.image_url"
                    (load)="onImageLoaded()"
                    alt="Visual map"
                    class="w-full"
                  />
                  <div
                    class="flex flex-wrap items-center absolute -top-4 right-0"
                  >
                    <ng-container
                      *ngTemplateOutlet="
                        deviceListTmpl;
                        context: {
                          devices$: disconnectedDevices$,
                          selectedDeviceId: selectedDeviceId
                        }
                      "
                    ></ng-container>
                  </div>
                </div>
              </ng-container>
            </div>
          } @else {
            <button mat-icon-button (click)="fileUpload.click()" class="mt-20">
              <mat-icon>cloud_upload</mat-icon>
            </button>
            <div>No background image</div>
            <div>Please upload a top down view of your location</div>
            <div class="text-sm text-slate-500">
              Maximum image size is 20 Mb
            </div>
          }
        </div>
      }
    </ng-container>
  </mat-drawer-content>
</mat-drawer-container>

<div class="fabs !fixed z-10 right-5 bottom-5 flex flex-col">
  <button
    mat-mini-fab
    [matTooltip]="'delete' | translate"
    (click)="deleteVisualMap()"
    matTooltipPosition="left"
    color="light"
    class="mb-2"
  >
    <mat-icon>delete</mat-icon>
  </button>
</div>

<input
  #fileUpload
  type="file"
  accept="image/png, image/jpeg"
  (change)="onImageSelected($event)"
  class="hidden"
/>

<ng-template
  #deviceListTmpl
  let-devices$="devices$"
  let-connected="connected"
  let-selectedDeviceId="selectedDeviceId"
>
  <ng-container *ngrxLet="devices$ as devices">
    @for (device of devices; track device.remote_id) {
      <ng-container
        *ngTemplateOutlet="
          deviceTmpl;
          context: {
            device: device,
            connected: connected,
            selectedDeviceId: selectedDeviceId
          }
        "
      ></ng-container>
    }
  </ng-container>
</ng-template>

<ng-template
  #deviceTmpl
  let-device="device"
  let-connected="connected"
  let-selectedDeviceId="selectedDeviceId"
>
  <div
    cdkDrag
    cdkDragBoundary=".map-boundary"
    [cdkDragFreeDragPosition]="
      device.position | visualMapPointToDisplay: vmImage
    "
    (cdkDragEnded)="deviceDragEnded(device, $event)"
    (click)="selectDevice(device)"
    [ngClass]="{
      'border-blue-700 opacity-100 font-bold':
        selectedDeviceId === device.remote_id,
      'opacity-80': selectedDeviceId !== device.remote_id,
      'absolute top-0 left-0': connected,
      'ms-2': !connected
    }"
    class="py-1 px-2 border-2 border-blue-300 bg-blue-300 rounded-lg cursor-move flex flex-col items-center justify-center"
  >
    <span class="whitespace-nowrap">
      {{ device | visualMapDeviceName: 8 }}
    </span>
    <ng-container *ngrxLet="device.remote_id | visualMapReadings as readings">
      @for (reading of readings ?? []; track reading) {
        <div class="text-xs whitespace-nowrap">
          @if (reading.value !== undefined && reading.value !== null) {
            {{ reading.value }}
          } @else {
            &mdash;
          }
          {{ reading.uom }}
        </div>
      }
    </ng-container>
  </div>
</ng-template>
