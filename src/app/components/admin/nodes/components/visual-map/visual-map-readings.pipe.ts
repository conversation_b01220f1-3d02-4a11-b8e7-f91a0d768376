import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { map } from 'rxjs';
import { nodesFeature } from '../../state/nodes.feature';

@Pipe({
  name: 'visualMapReadings',
  standalone: true,
})
export class VisualMapReadingsPipe implements PipeTransform {
  constructor(private store: Store) {}

  transform(id: number) {
    return this.store
      .select(nodesFeature.selectNodeItemById(id))
      .pipe(map((ni) => ni?.attributes.filter((a) => a.selected)));
  }
}
