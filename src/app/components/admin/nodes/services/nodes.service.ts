import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ColDef } from 'ag-grid-community';
import dayjs from 'dayjs';
import _, { isNumber } from 'lodash';
import { lastValueFrom, take } from 'rxjs';
import { IVariableToDisplay } from '../../../../api/api-sdk';
import { BaseDashboardService } from '../../../../common/base.dashboard.service';
import { AlertType } from '../../../../model/alert';
import { IConfiguredDevice } from '../../../../model/dashboard';
import { INodeListItem, IVariableAlert } from '../../../../model/node';
import { INodesState } from '../state/nodes.feature';

@Injectable({
  providedIn: 'root',
})
export class NodesService extends BaseDashboardService {
  constructor(private translate: TranslateService) {
    super();
  }

  getNodesAndAlerts(
    state: INodesState,
    cameras: IConfiguredDevice[],
    devices: IConfiguredDevice[],
  ) {
    const nodes: INodeListItem[] = [];
    const attrAlerts = new Map<string, Set<AlertType>>();

    // TODO: Use only cameras here, no need to pass devices
    for (const node of state.nodesData) {
      const camera = cameras.find((d) => d.remote_id === node.remote_id);
      const item: INodeListItem = {
        id: node.remote_id,
        name: node.node_name,
        date: dayjs.unix(node.timestamp_unix),
        cameraId: camera?.thermal_camera_id,
        cameraName: camera?.camera_name,
        lastReading: camera ? dayjs.unix(camera.timestamp_unix!) : undefined,
        attributes: [],
        alerts: [],
        visible: node.visible,
        allowedVariables: devices.find((d) => d.remote_id === node.remote_id)
          ?.allowed_variables,
        gauge: node.node_gauges?.[0]
          ? {
              enabled: node.node_gauges[0].enabled,
              variableName: node.node_gauges[0].variable_name,
              min: node.node_gauges[0].min_value,
              max: node.node_gauges[0].max_value,
              alertType: AlertType.unknown,
            }
          : undefined,
      };

      const attrs = _(state.attrGroups)
        .map((g) => g.attributes)
        .flatten()
        .value();

      for (const attr of attrs) {
        const value = node[attr.valuePath];
        const itemAttribute = {
          ...attr,
          value,
          alertType: this.getAlertType(
            value,
            attr?.alertMinValue,
            attr?.alertMaxValue,
          ),
        };

        if (itemAttribute.alertType !== AlertType.unknown) {
          if (!item.alerts.includes(itemAttribute.alertType)) {
            item.alerts.push(itemAttribute.alertType);
          }
          const attrAlertTypes =
            attrAlerts.get(attr.id) ?? new Set<AlertType>();
          attrAlertTypes.add(itemAttribute.alertType);
          attrAlerts.set(attr.id, attrAlertTypes);
        }

        if (attr.id === item.gauge?.variableName) {
          item.gauge.alertType = this.getAlertType(
            value,
            item.gauge.min,
            item.gauge.max,
          );
        }

        item.attributes.push(itemAttribute);
      }

      nodes.push(item);
    }
    return {
      nodes,
      alerts: Array.from(attrAlerts).map(
        ([id, alertTypes]) =>
          ({
            id,
            alertTypes: Array.from(alertTypes).sort(),
          }) satisfies IVariableAlert,
      ),
    };
  }

  async getReadingColumns(variables: IVariableToDisplay[]) {
    const staticColumns: ColDef[] = [
      {
        field: 'remote_id',
        headerName: await this.getString('remoteIDdevice'),
      },
      {
        field: 'node_name',
        headerName: await this.getString('nodeName'),
      },
      {
        field: 'date',
        comparator: (valueA, valueB, nodeA, nodeB, isDescending) => {
          const timestamp1 = nodeA.data.timestamp_unix;
          const timestamp2 = nodeB.data.timestamp_unix;
          if (timestamp1 == timestamp2) return 0;
          return timestamp1 > timestamp2 ? 1 : -1;
        },
      },
    ];

    const variableColumns: ColDef[] = [];

    for (const variable of variables) {
      const headerName = await this.getString(variable.name);
      variableColumns.push({
        field: variable.name,
        headerName,
        cellStyle: (params: { value: number }) => {
          const alertType = this.getAlertType(
            params.value,
            variable.min,
            variable.max,
          );
          return {
            backgroundColor:
              alertType === AlertType.high
                ? '#fce7f3'
                : alertType === AlertType.low
                  ? '#e0e7ff'
                  : '#fff',
          };
        },
      });
    }

    return [...staticColumns, ...variableColumns];
  }

  private async getString(key: string) {
    return await lastValueFrom(this.translate.get(key).pipe(take(1)));
  }

  private getAlertType(value: any, alertMinValue: any, alertMaxValue: any) {
    return isNumber(value)
      ? isNumber(alertMaxValue) && value >= alertMaxValue
        ? AlertType.high
        : isNumber(alertMinValue) && value <= alertMinValue
          ? AlertType.low
          : AlertType.unknown
      : AlertType.unknown;
  }
}
