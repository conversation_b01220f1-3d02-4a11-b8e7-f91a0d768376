<ng-container
  *ngrxLet="{
    chartData: chartData$,
    cameraIds: cameraIds$
  } as state"
>
  <div class="w-full h-full p-6 flex flex-col">
    <div class="mb-4 flex items-center">
      <app-date-range-picker
        [dateRange$]="dateRange$"
        (dateRangeChanged)="dateRangeChanged($event)"
        class="my-1 mr-4 flex-none"
      ></app-date-range-picker>
      <mat-chip-listbox multiple>
        @for (cameraId of state.cameraIds; track cameraId) {
          <mat-chip-option [selected]="true" (click)="toggleCamera(cameraId)">
            {{ "camera" | translate }} {{ cameraId }}
          </mat-chip-option>
        }
      </mat-chip-listbox>
    </div>
    <mat-grid-list cols="1" rowHeight="fit" gutterSize="14" class="h-full">
      <mat-grid-tile>
        <div
          class="flex items-center justify-center px-4 py-5 relative w-full h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
        >
          <canvas
            id="chat-canvas"
            baseChart
            [data]="state.chartData.data"
            [options]="state.chartData.options"
            [type]="'line'"
            class="!w-full !h-full cursor-pointer"
          >
          </canvas>
        </div>
      </mat-grid-tile>
    </mat-grid-list>
  </div>
</ng-container>
