import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { IChartData } from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';

export const TranspirationActions = createActionGroup({
  source: 'Transpiration',
  events: {
    dateRangeChanged: props<{ dateRange: DateRange }>(),
    regenerateChart: emptyProps(),
    regenerateChartSuccess: props<{
      transpirationChart: IChartData;
    }>(),
  },
});
