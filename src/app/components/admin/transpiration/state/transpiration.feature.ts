/* eslint-disable @typescript-eslint/no-explicit-any */

import { TranspirationActions } from './transpiration.actions';

import { createFeature, createReducer, createSelector, on } from '@ngrx/store';
import { produce } from 'immer';
import { cloneDeep } from 'lodash';
import { IChartData } from '../../../../model/dashboard';
import {
  DateRange,
  DateRangesPredefined,
  dateRanges,
} from '../../../../model/dateRange';

export interface ITranspirationState {
  dateRange: DateRange;
  transpirationChart: IChartData;
}

export const initialState: ITranspirationState = {
  dateRange: dateRanges.find(
    (dr) => dr.name === DateRangesPredefined.last7days,
  )!,
  transpirationChart: {
    data: { labels: [], datasets: [] },
    options: {},
    metadata: undefined,
  },
};

export const reducer = createReducer(
  initialState,
  on(TranspirationActions.regenerateChartSuccess, (state, action) =>
    produce(state, (draft) => {
      draft.transpirationChart = action.transpirationChart as any;
    }),
  ),
  on(TranspirationActions.dateRangeChanged, (state, action) =>
    produce(state, (draft) => {
      draft.dateRange = action.dateRange;
    }),
  ),
);

// TODO: unify this type of states with RTR etc
export const transpirationFeature = createFeature({
  name: 'Transpiration',
  reducer,
  extraSelectors: ({ selectTranspirationChart }) => ({
    selectTranspirationChartJsBinding: createSelector(
      selectTranspirationChart,
      (chartData) => cloneDeep(chartData),
    ),
  }),
});
