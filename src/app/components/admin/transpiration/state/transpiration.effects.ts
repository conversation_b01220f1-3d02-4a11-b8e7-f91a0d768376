import { Injectable } from '@angular/core';
import {
  Actions,
  OnInitEffects,
  concatLatestFrom,
  createEffect,
  ofType,
} from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { exhaustMap, filter, map } from 'rxjs';
import { AdminActions } from '../../../../state/admin/actions';
import { adminFeature } from '../../../../state/admin/feature';
import { TranspirationService } from '../services/transpiration.service';
import { TranspirationActions } from './transpiration.actions';
import { transpirationFeature } from './transpiration.feature';

@Injectable()
export class TranspirationEffects implements OnInitEffects {
  regenerateChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        TranspirationActions.dateRangeChanged,
        TranspirationActions.regenerateChart,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
        this.store.select(transpirationFeature.selectDateRange),
      ]),
      filter((values) => values.every((v) => !!v)),
      exhaustMap(([, activeLocation, devices, dateRange]) =>
        this.transpirationMng.calculateTranspiration(
          activeLocation!,
          devices,
          dateRange,
        ),
      ),
      filter((chartData) => !!chartData),
      map((chartData) =>
        TranspirationActions.regenerateChartSuccess({
          transpirationChart: chartData!.transpirationChart,
        }),
      ),
    );
  });

  ngrxOnInitEffects() {
    return TranspirationActions.regenerateChart();
  }

  constructor(
    private store: Store,
    private actions$: Actions,
    private transpirationMng: TranspirationService,
  ) {}
}
