import { Injectable } from '@angular/core';
import { Point } from 'chart.js';
import dayjs from 'dayjs';
import { lastValueFrom } from 'rxjs';
import { BaseDashboardService } from '../../../../common/base.dashboard.service';
import { ISigrowLocation } from '../../../../model/admin';
import {
  IChartData,
  IDevice,
  SigrowDataset,
  Uom,
} from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';
import { DeviceUtils } from '../../../../utils/device';

@Injectable({
  providedIn: 'root',
})
export class TranspirationService extends BaseDashboardService {
  async calculateTranspiration(
    location: ISigrowLocation,
    devices: IDevice[],
    dateRange: DateRange,
  ): Promise<{ transpirationChart: IChartData } | undefined> {
    const apiDateRange = this.getApiDateRange(dateRange, 'YYYYMMDD');

    const data = this.getEmptyChartData();
    const options = this.getEmptyChartOptions(dateRange);
    options!.interaction!.mode = 'nearest';
    options!.plugins!.tooltip!.enabled = true;

    for (const camera of DeviceUtils.applyCamerasFilter(devices)) {
      try {
        const cameraData = (
          await lastValueFrom(
            this.cameraApi.camTranspirationRetrieve(
              camera.thermal_camera_id,
              apiDateRange.date_begin,
              apiDateRange.date_end,
              4487,
            ),
          )
        ).data
          .map(
            (d) =>
              ({
                x: dayjs(d.date).valueOf(),
                y: d.transpiration,
              }) as Point,
          )
          .sort((p1, p2) => p1.x - p2.x);

        if (!cameraData.length) {
          continue;
        }

        const dataset: SigrowDataset = {
          data: cameraData,
          label: camera.name,
          pointRadius: 0,
          backgroundColor: 'transparent',
          deviceId: camera.thermal_camera_id,
          uom: Uom.celsius,
          variableName: camera.thermal_camera_id.toString(),
        };

        const color = this.getDatasetColor(dataset);
        dataset.borderColor = color;
        dataset.pointBackgroundColor = color;

        data.datasets.push(dataset);
      } catch (err) {
        console.log(err);
      }
    }

    // try {
    //   const ranges = [
    //     {
    //       start: 0,
    //       end: 25,
    //       color: ColorUtils.addAlpha('#E3F2FD', 0.4),
    //     },
    //     {
    //       start: 25,
    //       end: 1000,
    //       color: ColorUtils.addAlpha('#E8F5E9', 0.4),
    //     },
    //     {
    //       start: 1000,
    //       color: ColorUtils.addAlpha('#FFEBEE', 0.4),
    //     },
    //   ];
    //   options!.plugins!.annotation = {
    //     annotations: ranges.map(
    //       (range) =>
    //         ({
    //           drawTime: 'beforeDatasetsDraw',
    //           type: 'box',
    //           yMin: range.start,
    //           yMax: range.end,
    //           borderColor: 'transparent',
    //           backgroundColor: range.color,
    //         }) satisfies AnnotationOptions,
    //     ),
    //   };
    // } catch (err) {
    //   console.error(err);
    // }

    return {
      transpirationChart: {
        data,
        options,
        metadata: undefined,
      },
    };
  }
}
