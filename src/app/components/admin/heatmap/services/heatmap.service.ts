import { inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Point } from 'chart.js';
import { AnnotationOptions } from 'chartjs-plugin-annotation';
import dayjs from 'dayjs';
import _, { merge } from 'lodash';
import { lastValueFrom } from 'rxjs';
import { HeatmapRequest, IHeatmapData } from '../../../../api/api-sdk';
import { BaseDashboardService } from '../../../../common/base.dashboard.service';
import {
  IChartData,
  IDevice,
  SigrowDataset,
  Uom,
} from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';
import {
  DeviceValueType,
  IHeatmap,
  IHeatmapDevice,
  IHeatmapGridConfig,
} from '../../../../model/heatmap';

@Injectable({
  providedIn: 'root',
})
export class HeatmapService extends BaseDashboardService {
  translateService = inject(TranslateService);

  async fetchHeatmapData(
    centralId: number,
    devices: IDevice[],
    variable: string,
    dateRange: DateRange,
  ): Promise<IHeatmapData[]> {
    const options = this.getEmptyChartOptions(dateRange);
    options!.interaction!.mode = 'nearest';
    options!.plugins!.tooltip!.enabled = true;
    options!.plugins!.legend!.display = true;

    const dateFormat = 'YYYYMMDDHHmm';
    const { date_begin, date_end } = this.getApiDateRange(
      dateRange,
      dateFormat,
    );

    const heatmapData = await lastValueFrom(
      this.dataApi.heatmapCreate(
        centralId,
        new HeatmapRequest({
          devices: devices.map((d) => d.remote_id),
          variable,
          date_begin,
          date_end: dateRange.name.startsWith('last')
            ? dayjs(date_end, dateFormat).subtract(1, 'hour').format(dateFormat)
            : date_end,
        }),
      ),
    );

    return heatmapData.data;
  }

  async generateTempDeltaChart(
    variable: string,
    dateRange: DateRange,
    rawData: IHeatmapData[],
  ): Promise<IChartData> {
    const data = this.getEmptyChartData();
    const options = this.getEmptyChartOptions(dateRange);

    merge(options, {
      scales: {
        x: {
          type: 'time',
          time: {
            displayFormats: {
              hour: 'ddd H:mm',
            },
          },
        },
        y: { min: 0 },
      },
      interaction: {
        intersect: false,
        mode: 'nearest',
      },
      plugins: {
        tooltip: {
          enabled: true,
        },
        legend: {
          display: true,
        },
      },
    });

    const tempWarningLevelC = 2;

    if (
      variable === 'temp' &&
      Math.max(...rawData.map((d) => d.data.delta)) >= tempWarningLevelC
    ) {
      merge(options, {
        plugins: {
          annotation: {
            annotations: [
              {
                drawTime: 'beforeDatasetsDraw',
                type: 'line',
                yMin: tempWarningLevelC,
                yMax: tempWarningLevelC,
                borderColor: 'red',
                borderDash: [8, 4],
              } satisfies AnnotationOptions,
            ],
          },
        },
      });
    }

    const datasetData = rawData
      .filter((d) => d.data.delta > 0)
      .map(
        (d) =>
          ({
            x: this.getOuputTimestamp(d.timestamp),
            y: +d.data.delta.toFixed(2),
          }) as Point,
      )
      .sort((p1, p2) => p1.x - p2.x);

    const variableName = await lastValueFrom(
      this.translateService.get(variable),
    );
    const title = `${variableName} Difference`;

    const dataset: SigrowDataset = {
      data: datasetData,
      label: title,
      pointRadius: 0,
      pointHoverRadius: 6,
      pointHoverBackgroundColor: '#4318FF',
      pointHoverBorderColor: '#fff',
      pointHoverBorderWidth: 2,
      backgroundColor: 'transparent',
      deviceId: 0,
      uom: Uom.celsius,
      variableName: title,
    };
    this.assignDatasetColors(dataset);
    data.datasets.push(dataset);

    return {
      data,
      options,
      metadata: undefined,
    };
  }

  async generateHeatmapChart(
    timestamp: number,
    config: IHeatmapGridConfig,
    rawData: IHeatmapData[],
  ): Promise<IHeatmap | undefined> {
    if (!config.heatmap_data.rows.some((r) => r.devices.some((d) => !!d))) {
      return;
    }
    const heatmapSnapshots = _(rawData)
      .filter(
        (d) =>
          (!timestamp || this.getOuputTimestamp(d.timestamp) <= timestamp) &&
          d.data.delta > 0,
      )
      .orderBy((d) => d.timestamp, 'desc')
      .value();

    const heatmapDevices: IHeatmapDevice[] = [];

    for (const device of config.heatmap_data.rows
      .map((r) => r.devices)
      .flat()) {
      if (!device) {
        continue;
      }
      const deviceHeatmapData = heatmapSnapshots.find(
        (s) => !!s.data['devices'][device.remote_id],
      );
      if (deviceHeatmapData) {
        heatmapDevices.push({
          device,
          temperature: deviceHeatmapData.data['devices'][device.remote_id],
          timestamp: this.getOuputTimestamp(deviceHeatmapData.timestamp),
          valueType: undefined,
        });
      } else {
        heatmapDevices.push({
          device,
          temperature: 0,
          timestamp: 0,
          valueType: undefined,
        });
      }
    }

    const cols = config.heatmap_data.rows[0].devices.length;

    const temperatures = heatmapDevices.map((d) => d.temperature);
    const maxTemp = Math.max(...temperatures);
    const minTemp = Math.min(...temperatures);
    const maxDiff = maxTemp - minTemp;

    for (const maxTempDevice of heatmapDevices.filter(
      (d) => d.temperature === maxTemp,
    )) {
      maxTempDevice.valueType = DeviceValueType.max;
    }

    for (const minTempDevice of heatmapDevices.filter(
      (d) => d.temperature === minTemp,
    )) {
      minTempDevice.valueType = DeviceValueType.min;
    }

    let colorscale = undefined;

    if (maxDiff < 1) {
      colorscale = 'Greens';
    } else if (maxDiff < 2) {
      colorscale = 'YlGnBu';
    } else if (maxDiff >= 2) {
      colorscale = 'RdBu';
    }

    const heatmap: IHeatmap = {
      cols,
      devices: heatmapDevices,
      temperatures: this.splitArrayIntoColumns(temperatures, cols),
      colorscale,
    };

    return heatmap;
  }

  private splitArrayIntoColumns(arr: number[], cols: number): number[][] {
    const rows = Math.ceil(arr.length / cols);
    const result: number[][] = [];
    const avg = arr.reduce((a, b) => a + b) / arr.length;

    for (let i = 0; i < rows; i++) {
      result[i] = [];
      for (let j = 0; j < cols; j++) {
        const index = i * cols + j;
        result[i][j] = index < arr.length ? arr[index] : avg;
      }
    }

    return result;
  }

  private getOuputTimestamp(timestamp: string) {
    return dayjs(timestamp, 'YYYYMMDDHHmmss').unix() * 1000;
  }
}
