/* eslint-disable @typescript-eslint/no-explicit-any */

import { createFeature, createReducer, createSelector, on } from '@ngrx/store';
import { produce } from 'immer';
import { cloneDeep } from 'lodash';
import { IHeatmapData } from '../../../../api/api-sdk';
import { IChartData } from '../../../../model/dashboard';
import {
  DateRange,
  dateRanges,
  DateRangesPredefined,
} from '../../../../model/dateRange';
import {
  emptyHeatmap,
  IHeatmap,
  IHeatmapConfigRow,
  IHeatmapGridConfig,
} from '../../../../model/heatmap';
import { HeatmapActions } from './heatmap.actions';

export interface IHeatmapState {
  variable: string;
  dateRange: DateRange;
  rawData: IHeatmapData[];
  tempDeltaChart: IChartData;
  heatmapConfig: IHeatmapGridConfig;
  heatmap: IHeatmap;
}

export const initialState: IHeatmapState = {
  variable: 'temp',
  dateRange: dateRanges.find(
    (dr) => dr.name === DateRangesPredefined.last24hours,
  )!,
  rawData: [],
  tempDeltaChart: {
    data: { labels: [], datasets: [] },
    options: {},
    metadata: undefined,
  },
  heatmapConfig: {
    heatmap_data: {
      rows: [],
    },
    id: 0,
    central_id: 0,
    created_at: '',
    updated_at: '',
  },
  heatmap: { ...emptyHeatmap },
};

export const reducer = createReducer(
  initialState,
  on(HeatmapActions.variableChanged, (state, action) =>
    produce(state, (draft) => {
      draft.variable = action.variable;
    }),
  ),
  on(HeatmapActions.dateRangeChanged, (state, action) =>
    produce(state, (draft) => {
      draft.dateRange = action.dateRange;
    }),
  ),
  on(HeatmapActions.rawDataFetched, (state, action) =>
    produce(state, (draft) => {
      draft.rawData = action.data;
    }),
  ),
  on(HeatmapActions.regenerateTempDeltaChartSuccess, (state, action) =>
    produce(state, (draft) => {
      draft.tempDeltaChart = action.chartData as any;
    }),
  ),
  on(HeatmapActions.regenerateHeatmapSuccess, (state, action) =>
    produce(state, (draft) => {
      draft.heatmap = action.heatmap;
    }),
  ),
  on(HeatmapActions.addRow, (state, action) =>
    produce(state, (draft) => {
      const row: IHeatmapConfigRow = { devices: [] };
      const devicesCount =
        draft.heatmapConfig.heatmap_data.rows[0]?.devices?.length || 1;
      for (let i = 0; i < devicesCount; i++) {
        row.devices.push(undefined);
      }
      draft.heatmapConfig.heatmap_data.rows.splice(action.index + 1, 0, row);
    }),
  ),
  on(HeatmapActions.addColumn, (state, action) =>
    produce(state, (draft) => {
      for (const row of draft.heatmapConfig.heatmap_data.rows) {
        row.devices.splice(action.index + 1, 0, undefined);
      }
    }),
  ),
  on(HeatmapActions.removeRow, (state, action) =>
    produce(state, (draft) => {
      draft.heatmapConfig.heatmap_data.rows.splice(action.index, 1);
    }),
  ),
  on(HeatmapActions.removeColumn, (state, action) =>
    produce(state, (draft) => {
      for (const row of draft.heatmapConfig.heatmap_data.rows) {
        row.devices.splice(action.index, 1);
      }
    }),
  ),
  on(HeatmapActions.assignDevice, (state, action) =>
    produce(state, (draft) => {
      draft.heatmapConfig.heatmap_data.rows[action.rowIdx].devices[
        action.colIdx
      ] = action.device;
    }),
  ),
  on(
    HeatmapActions.setHeatmapConfig,
    HeatmapActions.updateHeatmapDevices,
    (state, action) =>
      produce(state, (draft) => {
        draft.heatmapConfig = { ...action.config };
      }),
  ),
);

// TODO: unify this type of states with RTR etc
export const heatmapFeature = createFeature({
  name: 'heatmap',
  reducer,
  extraSelectors: ({ selectTempDeltaChart, selectHeatmapConfig }) => ({
    selectTempDeltaChartJsBinding: createSelector(
      selectTempDeltaChart,
      (chartData) => cloneDeep(chartData),
    ),
    selectHeatmapDevices: createSelector(selectHeatmapConfig, (config) =>
      config.heatmap_data.rows.map((r) => r.devices.filter((d) => !!d)).flat(),
    ),
  }),
});
