import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType, OnInitEffects } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash';
import { exhaustMap, filter, map, switchMap } from 'rxjs';

import { HeatmapConfigs, UserApi } from '../../../../api/api-sdk';
import { emptyHeatmap, IHeatmapGridConfig } from '../../../../model/heatmap';
import { AdminActions } from '../../../../state/admin/actions';
import { adminFeature } from '../../../../state/admin/feature';
import { DateUtils } from '../../../../utils/date';
import { NodesActions } from '../../nodes/state/nodes.actions';
import { HeatmapService } from '../services/heatmap.service';
import { HeatmapActions } from './heatmap.actions';
import { heatmapFeature } from './heatmap.feature';

@Injectable()
export class HeatmapEffects implements OnInitEffects {
  private hasNoDevices(config?: IHeatmapGridConfig) {
    return (
      !config?.heatmap_data?.rows?.length ||
      config.heatmap_data.rows.every(
        (row) => !row.devices?.length || row.devices.every((device) => !device),
      )
    );
  }

  private getDefaultConfig(centralId: number): IHeatmapGridConfig {
    return {
      heatmap_data: {
        rows: [
          { devices: [undefined, undefined] },
          { devices: [undefined, undefined] },
        ],
      },
      central_id: centralId,
      id: -1,
      created_at: '',
      updated_at: '',
    };
  }

  loadHeatmap$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(AdminActions.activeLocationConfigRetrived, HeatmapActions.init),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
      ]),
      filter(([, location]) => !!location?.location),
      switchMap(([, location]) =>
        this.userApi
          .heatmapConfigsRetrieve(location!.location!.central_id)
          .pipe(
            map((config) => {
              let heatmapConfig = (
                config as unknown as IHeatmapGridConfig[]
              )[0];

              if (this.hasNoDevices(heatmapConfig)) {
                heatmapConfig = this.getDefaultConfig(
                  location!.location!.central_id,
                );
              }

              return HeatmapActions.setHeatmapConfig({ config: heatmapConfig });
            }),
          ),
      ),
    );
  });

  updateHeatmapDevices$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(NodesActions.nodeUpdated, HeatmapActions.setHeatmapConfig),
      concatLatestFrom(() => [
        this.store.select(heatmapFeature.selectHeatmapConfig),
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
      ]),
      map(([, heatmapConfig, devices]) => {
        const updatedConfig = cloneDeep(heatmapConfig);
        updatedConfig.heatmap_data.rows.forEach((row) => {
          row.devices = row.devices.map((device) =>
            devices.find((d) => d.remote_id === device?.remote_id),
          );
        });
        return HeatmapActions.updateHeatmapDevices({
          config: updatedConfig,
        });
      }),
    );
  });

  persistHeatmapConfig$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        HeatmapActions.addColumn,
        HeatmapActions.addRow,
        HeatmapActions.removeColumn,
        HeatmapActions.removeRow,
        HeatmapActions.assignDevice,
      ),
      concatLatestFrom(() => [
        this.store.select(heatmapFeature.selectHeatmapConfig),
        this.store.select(adminFeature.selectActiveLocation),
      ]),
      switchMap(([, config, location]) => {
        if (this.hasNoDevices(config)) {
          config = this.getDefaultConfig(location!.location!.central_id);
        }

        return this.userApi
          .heatmapConfigsCreate(
            new HeatmapConfigs({
              ...config,
              id: (config.id >= 0 ? config.id : undefined) as any,
            }),
          )
          .pipe(
            map((config) =>
              HeatmapActions.setHeatmapConfig({
                config: config as unknown as IHeatmapGridConfig,
              }),
            ),
          );
      }),
    );
  });

  regenerateChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        HeatmapActions.setHeatmapConfig,
        HeatmapActions.variableChanged,
        HeatmapActions.dateRangeChanged,
        HeatmapActions.regenerateChart,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(heatmapFeature.selectHeatmapDevices),
        this.store.select(heatmapFeature.selectVariable),
        this.store.select(heatmapFeature.selectDateRange),
      ]),
      switchMap(([, location, devices, variable, dateRange]) =>
        devices.length > 0
          ? this.heatmapMng.fetchHeatmapData(
              location!.location!.central_id,
              devices as any,
              variable,
              dateRange,
            )
          : Promise.resolve([]),
      ),
      map((data) => HeatmapActions.rawDataFetched({ data })),
    );
  });

  tempDeltaChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(HeatmapActions.rawDataFetched),
      concatLatestFrom(() => [
        this.store.select(heatmapFeature.selectVariable),
        this.store.select(heatmapFeature.selectDateRange),
        this.store.select(heatmapFeature.selectRawData),
      ]),
      filter((values) => values.every((v) => !!v)),
      exhaustMap(([, variable, dateRange, rawData]) =>
        this.heatmapMng.generateTempDeltaChart(variable, dateRange, rawData),
      ),
      map((chartData) =>
        HeatmapActions.regenerateTempDeltaChartSuccess({ chartData }),
      ),
    );
  });

  regenerateHeatmap$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        HeatmapActions.regenerateTempDeltaChartSuccess,
        HeatmapActions.updateHeatmapDevices,
      ),
      map(() => HeatmapActions.tempDeltaChartHover({ timestamp: 0 })),
    );
  });

  heatmap$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(HeatmapActions.tempDeltaChartHover),
      concatLatestFrom(() => [
        this.store.select(heatmapFeature.selectHeatmapConfig),
        this.store.select(heatmapFeature.selectRawData),
      ]),
      filter((values) => values.every((v) => !!v)),
      exhaustMap(([action, config, rawData]) =>
        this.heatmapMng.generateHeatmapChart(action.timestamp, config, rawData),
      ),
      map((heatmap) =>
        HeatmapActions.regenerateHeatmapSuccess({
          heatmap: heatmap ?? emptyHeatmap,
        }),
      ),
    );
  });

  shiftDateRange$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(HeatmapActions.shiftDateRange),
      concatLatestFrom(() => this.store.select(heatmapFeature.selectDateRange)),
      map(([action, dateRange]) =>
        DateUtils.getShiftedDateRange(dateRange, action.delta),
      ),
      map((newRange) =>
        HeatmapActions.dateRangeChanged({
          dateRange: newRange,
        }),
      ),
    );
  });

  ngrxOnInitEffects() {
    return HeatmapActions.init();
  }

  constructor(
    private store: Store,
    private actions$: Actions,
    private heatmapMng: HeatmapService,
    private userApi: UserApi,
  ) {}
}
