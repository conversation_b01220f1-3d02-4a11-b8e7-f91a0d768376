import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { IChartData, IDevice } from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';
import { IHeatmap, IHeatmapGridConfig } from '../../../../model/heatmap';
import { IHeatmapData } from './../../../../api/api-sdk';

export const HeatmapActions = createActionGroup({
  source: 'Heatmap',
  events: {
    init: emptyProps(),
    variableChanged: props<{ variable: string }>(),
    dateRangeChanged: props<{ dateRange: DateRange }>(),
    shiftDateRange: props<{ delta: number }>(),
    rawDataFetched: props<{ data: IHeatmapData[] }>(),
    regenerateTempDeltaChartSuccess: props<{
      chartData: IChartData;
    }>(),
    regenerateHeatmapSuccess: props<{
      heatmap: IHeatmap;
    }>(),
    regenerateChart: emptyProps(),
    tempDeltaChartHover: props<{ timestamp: number }>(),
    addColumn: props<{ index: number }>(),
    addRow: props<{ index: number }>(),
    removeRow: props<{ index: number }>(),
    removeColumn: props<{ index: number }>(),
    assignDevice: props<{ colIdx: number; rowIdx: number; device: IDevice }>(),
    setHeatmapConfig: props<{ config: IHeatmapGridConfig }>(),
    updateHeatmapDevices: props<{ config: IHeatmapGridConfig }>(),
  },
});
