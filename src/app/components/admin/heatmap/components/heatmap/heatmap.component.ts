import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { ChartEvent, Point } from 'chart.js';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import Plotly from 'plotly.js-dist-min';
import { Observable, tap } from 'rxjs';
import { IVariableToDisplay } from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { DpDateFormatPipe } from '../../../../../common/pipes/dayjs/dp-date-format.pipe';
import { DpFromUnixMSPipe } from '../../../../../common/pipes/dayjs/dp-from-unix-ms.pipe';
import { DeviceNamePipe } from '../../../../../common/pipes/device-name.pipe';
import SharedModules from '../../../../../common/shared.modules';
import { IChartData, IDevice } from '../../../../../model/dashboard';
import { DateRange } from '../../../../../model/dateRange';
import {
  DeviceValueType,
  IHeatmap,
  IHeatmapGridConfig,
} from '../../../../../model/heatmap';
import { adminFeature } from '../../../../../state/admin/feature';
import { DateRangePickerComponent } from '../../../../common/date-range-picker/date-range-picker.component';
import { HeatmapActions } from '../../state/heatmap.actions';
import { heatmapFeature } from '../../state/heatmap.feature';

enum HeatmapMode {
  chart,
  config,
}

@Component({
  selector: 'app-heatmap',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    NgChartsModule,
    ReactiveFormsModule,
    DateRangePickerComponent,
    LetDirective,
    DeviceNamePipe,
    DpFromUnixMSPipe,
    DpDateFormatPipe,
  ],
  templateUrl: './heatmap.component.html',
  styleUrl: './heatmap.component.scss',
})
export class HeatmapComponent extends BaseComponent implements OnInit {
  @ViewChild(BaseChartDirective) chart!: BaseChartDirective;

  tempDeltaChartData$!: Observable<IChartData>;
  heatmapConfig$!: Observable<IHeatmapGridConfig>;
  heatmap$!: Observable<IHeatmap>;
  dateRange$!: Observable<DateRange>;
  devices$!: Observable<IDevice[]>;
  variables$!: Observable<IVariableToDisplay[]>;

  mode = HeatmapMode.chart;
  modes = HeatmapMode;
  valueTypes = DeviceValueType;

  variableCtrl = new FormControl('');

  currentTimestamp?: number;

  private colIdx = -1;
  private rowIdx = -1;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.tempDeltaChartData$ = this.store.select(
      heatmapFeature.selectTempDeltaChartJsBinding,
    );
    this.dateRange$ = this.store.select(heatmapFeature.selectDateRange);
    this.devices$ = this.store.select(
      adminFeature.selectRemoteForActiveLocation,
    );
    this.variables$ = this.store.select(adminFeature.selectVariables);
    this.heatmapConfig$ = this.store.select(heatmapFeature.selectHeatmapConfig);
    this.heatmap$ = this.store.select(heatmapFeature.selectHeatmap).pipe(
      tap((heatmap) => {
        setTimeout(() => {
          this.plotHeatmap(heatmap);
        });
      }),
    );

    // Initialize form control from state
    this.subSafe(
      this.store.select(heatmapFeature.selectVariable),
      (variable) => {
        if (variable && variable !== this.variableCtrl.value) {
          this.variableCtrl.setValue(variable, { emitEvent: false });
        }
      },
    );

    // Subscribe to form control changes
    this.subSafe(this.variableCtrl.valueChanges, (variable) => {
      if (variable) {
        this.store.dispatch(HeatmapActions.variableChanged({ variable }));
      }
    });
  }

  dateRangeChanged(dateRange: DateRange) {
    this.store.dispatch(HeatmapActions.dateRangeChanged({ dateRange }));
  }

  shiftDateRange(delta: number) {
    this.store.dispatch(HeatmapActions.shiftDateRange({ delta }));
  }

  onChartHover(chartEvent: {
    event?: ChartEvent | undefined;
    active?: object[] | undefined;
  }) {
    if (!chartEvent.active || chartEvent.active.length === 0) {
      return;
    }

    const activePoint = chartEvent.active[0] as {
      index: number;
    };
    const chartData = this.chart.chart?.config.data;
    if (!chartData || !activePoint) {
      return;
    }

    const timestamp = (chartData.datasets?.[0].data[activePoint.index] as Point)
      .x;
    if (timestamp) {
      this.store.dispatch(
        HeatmapActions.tempDeltaChartHover({
          timestamp,
        }),
      );
    }

    this.currentTimestamp = timestamp;
  }

  regenerateHeatmap() {
    this.store.dispatch(HeatmapActions.regenerateChart());
  }

  deviceComparer(d1: IDevice, d2: IDevice) {
    return d1.remote_id === d2.remote_id;
  }

  addColumn(index: number) {
    this.store.dispatch(HeatmapActions.addColumn({ index }));
  }

  addRow(index: number) {
    this.store.dispatch(HeatmapActions.addRow({ index }));
  }

  removeColumn(index: number) {
    this.store.dispatch(HeatmapActions.removeColumn({ index }));
  }

  removeRow(index: number) {
    this.store.dispatch(HeatmapActions.removeRow({ index }));
  }

  setCurrentDeviceCoords(colIdx: number, rowIdx: number) {
    this.colIdx = colIdx;
    this.rowIdx = rowIdx;
  }

  assignDevice(device: IDevice) {
    this.store.dispatch(
      HeatmapActions.assignDevice({
        colIdx: this.colIdx,
        rowIdx: this.rowIdx,
        device,
      }),
    );
  }

  configureHeatmap() {
    this.mode = HeatmapMode.config;
  }

  displayHeatmap() {
    this.store.dispatch(HeatmapActions.tempDeltaChartHover({ timestamp: 0 }));
    this.mode = HeatmapMode.chart;
  }

  private plotHeatmap(heatmap: IHeatmap) {
    if (!document.getElementById('heatmap')) {
      return;
    }

    const heatmapData: Plotly.Data[] = [
      {
        z: heatmap.temperatures,
        zsmooth: 'best',
        type: 'heatmap',
        colorscale: heatmap.colorscale,
        showscale: false,
      },
    ];

    const layout: Partial<Plotly.Layout> = {
      xaxis: {
        visible: false,
      },
      yaxis: {
        visible: false,
        autorange: 'reversed',
      },
      margin: {
        t: 0,
        r: 0,
        b: 0,
        l: 0,
      },
      hovermode: false,
    };

    Plotly.newPlot('heatmap', heatmapData, layout, {
      displayModeBar: false,
    });
  }
}
