<ng-container
  *ngrxLet="{
    devices: devices$,
    variables: variables$,
    tempDeltaChartData: tempDeltaChartData$,
    heatmapConfig: heatmapConfig$,
    heatmap: heatmap$
  } as state"
>
  <div class="w-full h-full p-6 flex flex-col">
    <div class="mb-4 flex items-center">
      <app-date-range-picker
        [dateRange$]="dateRange$"
        [showShiftControls]="true"
        (dateRangeChanged)="dateRangeChanged($event)"
        (shiftDateRange)="shiftDateRange($event)"
        class="me-4 flex-none"
      ></app-date-range-picker>

      <button
        mat-flat-button
        [matMenuTriggerFor]="varsMenu"
        class="!bg-stroke z-1"
      >
        {{ variableCtrl.value! | translate }}
      </button>
      <mat-menu #varsMenu="matMenu">
        @for (variable of state.variables; track variable) {
          <button mat-menu-item (click)="variableCtrl.setValue(variable.name)">
            {{ variable.name | translate }}
          </button>
        }
      </mat-menu>

      <span class="flex-1"></span>
      @switch (mode) {
        @case (modes.chart) {
          <button
            mat-flat-button
            color="primary"
            (click)="configureHeatmap()"
            class="ms-4 flex-none"
          >
            Configure heatmap
          </button>
        }
        @case (modes.config) {
          <button
            mat-flat-button
            color="primary"
            (click)="displayHeatmap()"
            class="ms-4 flex-none"
          >
            Display heatmap
          </button>
        }
      }
      <button
        mat-flat-button
        color="primary"
        (click)="regenerateHeatmap()"
        class="ms-4 flex-none"
      >
        {{ "refresh" | translate }}
      </button>
    </div>
    <mat-grid-list cols="2" rowHeight="fit" gutterSize="14" class="h-full">
      <mat-grid-tile>
        <div
          class="flex items-center justify-center px-4 py-5 relative w-full h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
        >
          @if (!state.tempDeltaChartData.data) {
            <div
              class="absolute flex flex-col items-center justify-center bg-white w-full h-full z-99 rounded text-complementary"
            >
              <mat-icon class="text-8xl !w-24 !h-24"> query_stats </mat-icon>
              <div class="text-2xl text-center font-medium mt-4">
                Select devices to display
              </div>
            </div>
          }
          <canvas
            id="chat-canvas"
            baseChart
            [type]="'line'"
            [data]="state.tempDeltaChartData.data"
            [options]="state.tempDeltaChartData.options"
            (chartHover)="onChartHover($event)"
            class="!w-full !h-full cursor-pointer"
          >
          </canvas>
        </div>
      </mat-grid-tile>
      @switch (mode) {
        @case (modes.chart) {
          <mat-grid-tile>
            <div
              class="flex items-center justify-center px-4 py-5 relative w-full h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
            >
              @if (currentTimestamp) {
                <div
                  class="absolute z-10 top-8 w-48 text-center bg-white px-2 rounded opacity-80"
                >
                  {{
                    currentTimestamp
                      | dpFromUnixMS
                      | dpDateFormat: "DD/MM/YYYY HH:mm:ss"
                  }}
                </div>
              }
              <div id="heatmap" class="w-full h-full"></div>
              <mat-grid-list
                [cols]="state.heatmap.cols"
                rowHeight="fit"
                gutterSize="0"
                class="w-full h-full !absolute"
              >
                @for (
                  device of state.heatmap.devices;
                  track device;
                  let idx = $index
                ) {
                  <mat-grid-tile>
                    <div
                      class="w-full h-full flex items-center justify-center text-center"
                    >
                      <div class="flex flex-col items-center justify-center">
                        <div class="bg-white px-2 rounded opacity-80">
                          {{ device.device | deviceName }}
                        </div>
                        <div
                          class="bg-white px-2 rounded opacity-80 mt-1 flex items-center justify-center"
                        >
                          {{ device.temperature | number: "1.1-2" }}
                          @switch (device.valueType) {
                            @case (valueTypes.max) {
                              <div class="text-rose-700 ms-1">▲</div>
                            }
                            @case (valueTypes.min) {
                              <div class="text-blue-700 ms-1">▼</div>
                            }
                          }
                        </div>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
              </mat-grid-list>
            </div>
          </mat-grid-tile>
        }
        @case (modes.config) {
          @if (state.heatmapConfig.heatmap_data.rows.length) {
            <mat-grid-tile>
              <div
                class="flex items-center justify-center px-4 py-5 relative w-full h-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
              >
                <mat-grid-list
                  [cols]="
                    state.heatmapConfig.heatmap_data.rows[0].devices.length
                  "
                  rowHeight="fit"
                  gutterSize="0"
                  class="w-full h-full !absolute"
                >
                  @for (
                    row of state.heatmapConfig.heatmap_data.rows;
                    track row;
                    let rowIdx = $index
                  ) {
                    @for (
                      device of row.devices;
                      track device;
                      let colIdx = $index
                    ) {
                      <mat-grid-tile>
                        <div
                          class="w-full h-full relative border border-slate-300 flex items-center justify-center text-center"
                        >
                          <div
                            class="absolute top-2 w-full flex items-center justify-center"
                          >
                            <button
                              mat-icon-button
                              [matMenuTriggerFor]="actions"
                            >
                              <mat-icon class="text-slate-400"
                                >more_horiz</mat-icon
                              >
                            </button>
                            <mat-menu #actions="matMenu">
                              <button mat-menu-item (click)="addColumn(colIdx)">
                                Add column on the right
                              </button>
                              <button mat-menu-item (click)="addRow(rowIdx)">
                                Add row below
                              </button>
                              <button
                                mat-menu-item
                                (click)="removeColumn(colIdx)"
                              >
                                Remove column
                              </button>
                              <button mat-menu-item (click)="removeRow(rowIdx)">
                                Remove row
                              </button>
                            </mat-menu>
                          </div>

                          <button
                            mat-button
                            [matMenuTriggerFor]="devices"
                            (click)="setCurrentDeviceCoords(colIdx, rowIdx)"
                            class="m-4 !h-auto min-h-[36px]"
                          >
                            @if (device) {
                              <div class="text-xl">
                                {{ device | deviceName }}
                              </div>
                            } @else {
                              <span class="text-slate-400"> Empty cell </span>
                            }
                          </button>
                        </div>
                      </mat-grid-tile>
                    }
                  }
                </mat-grid-list>
              </div>
            </mat-grid-tile>
          }
        }
      }
    </mat-grid-list>
  </div>
  <mat-menu #devices="matMenu">
    @for (device of state.devices; track device) {
      <button mat-menu-item (click)="assignDevice(device)">
        {{ device | deviceName }}
      </button>
    }
  </mat-menu>
</ng-container>
