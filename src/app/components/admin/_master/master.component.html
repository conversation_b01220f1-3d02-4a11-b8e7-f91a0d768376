<ng-container
  *ngrxLet="{
    isMobile: ui.isMobile$,
    locations: locations$,
    user: user$,
    banners: banners$,
    bannerParams: ui.bannerParams$
  } as state"
>
  <app-banners></app-banners>

  <mat-toolbar
    [style.top.px]="(state.banners?.length ?? 0) * state.bannerParams.height"
    color="primary"
    class="relative app-header drop-shadow-1 shadow-2 !pl-0"
  >
    @if (state.isMobile) {
      <div class="absolute w-full left-0 flex items-center justify-center">
        <img
          routerLink="/admin/map"
          src="assets/img/logo_white.svg"
          alt="logo"
          class="h-10 cursor-pointer"
        />
      </div>
      <div class="flex-grow"></div>
      <button mat-icon-button [matMenuTriggerFor]="mobileMenu">
        <mat-icon class="text-white">more_vert</mat-icon>
      </button>
      <mat-menu #mobileMenu="matMenu">
        <ng-container *ngrxLet="appSections$ as appSections">
          @for (appSection of appSections; track appSection) {
            @for (
              menuItem of appSection.menuItems ?? [appSection];
              track menuItem
            ) {
              <button mat-menu-item [routerLink]="menuItem.url">
                <mat-icon color="primary">{{ menuItem.icon }}</mat-icon>
                {{ menuItem.title | translate }}
              </button>
            }
          }
        </ng-container>
        <a mat-menu-item href="https://help.sigrow.com/" target="_blank">
          <mat-icon color="primary">help</mat-icon>
          {{ "help" | translate }}
        </a>
        <button mat-menu-item routerLink="/admin/settings">
          <mat-icon color="primary">settings</mat-icon>
          {{ "configuration" | translate }}
        </button>
        <button mat-menu-item [matMenuTriggerFor]="locationsMenu">
          <mat-icon color="primary">place</mat-icon>
          {{ "location" | translate }}
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon color="primary">logout</mat-icon>
          {{ "logout" | translate }} {{ state.user?.userName }}
        </button>
      </mat-menu>
      <mat-menu #locationsMenu="matMenu">
        @for (location of state.locations; track location) {
          <button mat-menu-item (click)="setActiveLocation(location)">
            {{ location.name }}
          </button>
        }
      </mat-menu>
    } @else {
      <div class="w-70 h-full flex items-center justify-center">
        <img
          routerLink="/admin/map"
          src="assets/img/logo_white.svg"
          alt="logo"
          class="h-10 cursor-pointer"
        />
      </div>
      <div class="absolute w-full text-center left-0 -z-10">
        <ng-container *ngrxLet="appSections$ as appSections">
          @for (appSection of appSections; track appSection) {
            @if (appSection.hasMenu) {
              <button
                mat-flat-button
                [matMenuTriggerFor]="sectionMenu"
                color="primary"
                class="me-3 !px-6"
              >
                <mat-icon class="!text-[16px] !pt-[1px]">{{
                  appSection.icon
                }}</mat-icon>
                {{ appSection.title | translate }}
              </button>
              <mat-menu #sectionMenu="matMenu">
                @for (menuItem of appSection.menuItems; track menuItem) {
                  <button mat-menu-item [routerLink]="menuItem.url">
                    <mat-icon class="p-[1px] text-[22px]">{{
                      menuItem.icon
                    }}</mat-icon>
                    {{ menuItem.title }}
                  </button>
                }
              </mat-menu>
            } @else {
              <button
                mat-flat-button
                [routerLink]="appSection.url"
                color="primary"
                routerLinkActive="app-nav-active"
                class="me-3 !px-6"
              >
                <mat-icon>{{ appSection.icon }}</mat-icon>
                {{ appSection.title | translate }}
              </button>
            }
          }
        </ng-container>
      </div>
      <div class="flex-grow"></div>

      <div>
        <button
          mat-button
          [matMenuTriggerFor]="locationsMenu"
          [matTooltip]="'location' | translate"
        >
          <ng-container *ngrxLet="activeLocation$ as activeLocation">
            <div class="flex items-center justify-center text-white">
              {{ activeLocation?.location?.name }}
              <mat-icon>expand_more</mat-icon>
            </div>
          </ng-container>
        </button>
        <mat-menu #locationsMenu="matMenu" xPosition="before">
          <div
            (click)="$event.stopPropagation()"
            subscriptSizing="'dynamic'"
            class="p-"
          >
            <mat-form-field class="w-full !-mt-2">
              <mat-label>{{ "search" | translate }}</mat-label>
              <input matInput [formControl]="searchCtrl" />
              <button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="searchCtrl.setValue(undefined)"
                [disabled]="!searchCtrl.value"
              >
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          @for (
            location of state.locations;
            track location;
            let first = $first, last = $last
          ) {
            <button
              mat-menu-item
              (click)="setActiveLocation(location)"
              [ngClass]="{ '!-mt-6': first }"
            >
              <div class="py-3">
                <div class="font-medium">
                  {{ location.name }}
                </div>
                <div class="mt-0.5">
                  <span
                    class="me-3 bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300"
                  >
                    {{ location.remotes.length }} {{ "sensors" | translate }}
                  </span>
                  @if (location.cameras.length) {
                    <span
                      class="bg-blue-100 text-blue-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300"
                    >
                      {{ location.cameras.length }} {{ "cameras" | translate }}
                    </span>
                  }
                </div>
              </div>
            </button>
            @if (!last) {
              <mat-divider></mat-divider>
            }
          }
        </mat-menu>
      </div>

      <div>
        <button
          mat-icon-button
          [matTooltip]="'contactSupport' | translate"
          (click)="contactUs()"
        >
          <mat-icon class="text-white">forum</mat-icon>
        </button>
      </div>

      <div>
        <button
          mat-icon-button
          [matMenuTriggerFor]="userMenu"
          [matTooltip]="'user' | translate"
        >
          <mat-icon class="text-white">account_circle</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu" xPosition="before">
          <div
            class="text-center p-4 pt-2 mb-2 border-b border-stone-300 text-black"
          >
            {{ state.user?.userName }}
          </div>
          <button mat-menu-item (click)="logout()">
            {{ "logout" | translate }}
          </button>
        </mat-menu>
      </div>

      <div>
        <a
          mat-icon-button
          href="https://help.sigrow.com/"
          target="_blank"
          [matTooltip]="'help' | translate"
        >
          <mat-icon class="text-white">help</mat-icon>
        </a>
      </div>

      <div>
        <button
          mat-icon-button
          routerLink="/admin/settings"
          [matTooltip]="'configuration' | translate"
        >
          <mat-icon class="text-white">settings</mat-icon>
        </button>
      </div>
    }
  </mat-toolbar>

  <mat-sidenav-container
    [style.top.px]="
      state.bannerParams.topOffset +
      (state.banners?.length ?? 0) * state.bannerParams.height
    "
    class="app-container !z-auto"
  >
    <mat-sidenav-content class="!z-auto lg:z-1">
      <ng-container *ngrxLet="ui.loading$ as loading">
        @if (loading) {
          <mat-progress-bar
            mode="indeterminate"
            class="!absolute left-0 top-0 z-10"
          ></mat-progress-bar>
        }
      </ng-container>
      <router-outlet></router-outlet>
    </mat-sidenav-content>
  </mat-sidenav-container>
</ng-container>
