import { CommonModule } from '@angular/common';
import { Component, OnInit, inject, isDevMode } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { RouterModule, RouterOutlet } from '@angular/router';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable, combineLatest, map, startWith } from 'rxjs';
import { IBanner, ILocationsData } from '../../../api/api-sdk';
import { APP_VERSION } from '../../../app.config';
import { BaseComponent } from '../../../common/base.component';
import SharedModules from '../../../common/shared.modules';
import { ISigrowLocation } from '../../../model/admin';
import { IAppSection } from '../../../model/app';
import { IUser } from '../../../model/user';
import { adminFeature } from '../../../state/admin/feature';
import { BannersComponent } from '../../common/banners/banners.component';
import { AdminActions } from './../../../state/admin/actions';
import { AuthActions } from './../../../state/auth/actions';
import { authFeature } from './../../../state/auth/feature';

@Component({
  selector: 'app-master',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    RouterOutlet,
    BannersComponent,
    LetDirective,
    MatMenuModule,
  ],
  templateUrl: './master.component.html',
  styleUrl: './master.component.scss',
})
export class MasterComponent extends BaseComponent implements OnInit {
  locations$!: Observable<ILocationsData[]>;
  activeLocation$!: Observable<ISigrowLocation | undefined>;
  user$!: Observable<IUser | undefined>;
  banners$!: Observable<IBanner[] | undefined>;
  isFlowerRecognitionAvailable$!: Observable<boolean>;
  isBiomassAvailable$!: Observable<boolean>;

  appSections$!: Observable<IAppSection[]>;
  searchCtrl = new FormControl<string | undefined>(undefined);

  appVersion = inject(APP_VERSION);

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.store.dispatch(AdminActions.initAdmin());

    this.locations$ = combineLatest([
      this.store.select(adminFeature.selectLocations),
      this.searchCtrl.valueChanges.pipe(startWith(undefined)),
    ]).pipe(
      map(([locations, search]) =>
        search
          ? locations.filter(
              (l) => l.name.toLowerCase().indexOf(search.toLowerCase()) >= 0,
            )
          : locations,
      ),
    );
    this.activeLocation$ = this.store.select(adminFeature.selectActiveLocation);
    this.user$ = this.store.select(authFeature.selectUser);
    this.banners$ = this.store.select(adminFeature.selectBannersToDisplay);
    this.appSections$ = combineLatest([
      this.store.select(adminFeature.selectIsCamerasAvailable),
      this.store.select(adminFeature.selectIsFlowerRecognitionAvailable),
      this.store.select(adminFeature.selectIsBiomassAvailable),
    ]).pipe(
      map(([cameras, flowersRec, biomass]) => {
        const appSections: IAppSection[] = [];

        appSections.push(
          {
            url: ['/admin/map'],
            title: 'nodesMap',
            icon: 'hub',
            hasMenu: true,
            menuItems: [
              { url: ['/admin/map'], title: 'Maps', icon: 'hub' },
              { url: ['/admin/heatmap'], title: 'Heatmap', icon: 'thermostat' },
            ],
          },
          { url: ['/admin/charts'], title: 'charts', icon: 'bar_chart' },
        );

        if (cameras) {
          appSections.push({
            url: ['/admin/cameras'],
            title: 'cameras',
            icon: 'camera',
          });
        }

        appSections.push({
          url: ['/admin/table'],
          title: 'readings',
          icon: 'table',
        });

        if (isDevMode()) {
          if (flowersRec) {
            appSections.push(
              // { url: ['/admin/rtr'], title: 'RTR', icon: 'engineering' },
              {
                url: ['/admin/transpiration'],
                title: 'transpiration',
                icon: 'water_drop',
              },
            );
          }

          if (biomass) {
            appSections.push({
              url: ['/admin/biomass'],
              title: 'biomass',
              icon: 'grass',
            });
          }
        }

        return appSections;
      }),
    );

    console.log(`Sigrow Admin App v${isDevMode() ? 'DEV' : this.appVersion}`);
  }

  setActiveLocation(location: ILocationsData) {
    this.store.dispatch(AdminActions.activeLocationChanged({ location }));
  }

  logout() {
    this.store.dispatch(AuthActions.logout());
  }

  contactUs() {
    (window as any).FreshworksWidget('open');
  }
}
