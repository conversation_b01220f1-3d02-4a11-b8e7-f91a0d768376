import { CommonModule } from '@angular/common';
import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { Observable, combineLatest, map } from 'rxjs';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { CameraDataset, IRTRChartData } from '../../../../../model/dashboard';
import { DateRange } from '../../../../../model/dateRange';
import { DateRangePickerComponent } from '../../../../common/date-range-picker/date-range-picker.component';
import { RTRActions } from '../../state/rtr.actions';
import { rtrFeature } from '../../state/rtr.feature';

@Component({
  selector: 'app-rtr',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    NgChartsModule,
    DateRangePickerComponent,
    LetDirective,
  ],
  templateUrl: './rtr.component.html',
  styleUrl: './rtr.component.scss',
})
export class RtrComponent extends BaseComponent implements OnInit {
  @ViewChildren(BaseChartDirective) charts?: QueryList<BaseChartDirective>;

  rtrDailyChartData$!: Observable<IRTRChartData>;
  rtrTendencyChartData$!: Observable<IRTRChartData>;
  dateRange$!: Observable<DateRange>;
  cameraIds$!: Observable<number[]>;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.rtrDailyChartData$ = this.store.select(
      rtrFeature.selectRtrDailyChatJsBinding,
    );
    this.rtrTendencyChartData$ = this.store.select(
      rtrFeature.selectRtrTendencyChatJsBinding,
    );
    this.dateRange$ = this.store.select(rtrFeature.selectDateRange);
    this.cameraIds$ = combineLatest([
      this.rtrDailyChartData$,
      this.rtrTendencyChartData$,
    ]).pipe(
      map(([dailyData, tendencyData]) =>
        Array.from(
          new Set([
            ...dailyData.data.datasets.map((ds) => ds.cameraId),
            ...tendencyData.data.datasets.map((ds) => ds.cameraId),
          ]),
        ),
      ),
    );
  }

  dateRangeChanged(dateRange: DateRange) {
    this.store.dispatch(RTRActions.dateRangeChanged({ dateRange }));
  }

  toggleCamera(cameraId: number) {
    for (const chart of this.charts?.toArray() ?? []) {
      const cameraDatasets = chart.data?.datasets?.filter(
        (ds) => (ds as CameraDataset).cameraId === cameraId,
      );
      for (const cameraDS of cameraDatasets ?? []) {
        cameraDS.hidden = !cameraDS.hidden;
      }
      chart.update();
    }
  }
}
