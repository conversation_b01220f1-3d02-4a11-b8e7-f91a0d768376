import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { IRTRChartData } from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';

export const RTRActions = createActionGroup({
  source: 'RTR',
  events: {
    dateRangeChanged: props<{ dateRange: DateRange }>(),
    regenerateChart: emptyProps(),
    regenerateChartSuccess: props<{
      rtrDaily: IRTRChartData;
      rtrTendency: IRTRChartData;
    }>(),
  },
});
