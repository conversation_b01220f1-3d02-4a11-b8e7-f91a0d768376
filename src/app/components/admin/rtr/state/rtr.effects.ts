import { Injectable } from '@angular/core';
import { Actions, OnInitEffects, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { exhaustMap, filter, map } from 'rxjs';

import { adminFeature } from '../../../../state/admin/feature';
import { RtrService } from '../services/rtr.service';
import { AdminActions } from './../../../../state/admin/actions';
import { RTRActions } from './rtr.actions';
import { rtrFeature } from './rtr.feature';

@Injectable()
export class RTREffects implements OnInitEffects {
  // TODO: add cache here to load RTR data only once, no need to reload on location change
  regenerateChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        RTRActions.dateRangeChanged,
        RTRActions.regenerateChart,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(rtrFeature.selectDateRange),
      ]),
      filter((values) => values.every((v) => !!v)),
      exhaustMap(([, activeLocation, dateRange]) =>
        this.rtrMng.calculateRTR(activeLocation!, dateRange),
      ),
      filter((chartData) => !!chartData),
      map((chartData) =>
        RTRActions.regenerateChartSuccess({
          rtrDaily: chartData!.rtrDaily,
          rtrTendency: chartData!.rtrTendency,
        }),
      ),
    );
  });

  ngrxOnInitEffects() {
    return RTRActions.regenerateChart();
  }

  constructor(
    private store: Store,
    private actions$: Actions,
    private rtrMng: RtrService,
  ) {}
}
