/* eslint-disable @typescript-eslint/no-explicit-any */

import { CameraDataset, IRTRChartData } from '../../../../model/dashboard';
import { RTRActions } from './rtr.actions';

import { createFeature, createReducer, createSelector, on } from '@ngrx/store';
import { produce } from 'immer';
import { cloneDeep } from 'lodash';
import { ISigrowLocation } from '../../../../model/admin';
import {
  DateRange,
  DateRangesPredefined,
  dateRanges,
} from '../../../../model/dateRange';
import { adminFeature } from '../../../../state/admin/feature';

export interface IRTRState {
  dateRange: DateRange;
  rtrDailyChartData: IRTRChartData;
  rtrTendencyChartData: IRTRChartData;
}

export const initialState: IRTRState = {
  dateRange: dateRanges.find(
    (dr) => dr.name === DateRangesPredefined.last7days,
  )!,
  rtrDailyChartData: { data: { labels: [], datasets: [] }, options: {} },
  rtrTendencyChartData: { data: { labels: [], datasets: [] }, options: {} },
};

export const reducer = createReducer(
  initialState,
  on(RTRActions.regenerateChartSuccess, (state, action) =>
    produce(state, (draft) => {
      draft.rtrDailyChartData = action.rtrDaily as any;
      draft.rtrTendencyChartData = action.rtrTendency as any;
    }),
  ),
  on(RTRActions.dateRangeChanged, (state, action) =>
    produce(state, (draft) => {
      draft.dateRange = action.dateRange;
    }),
  ),
);

export const rtrFeature = createFeature({
  name: 'RTR',
  reducer,
  extraSelectors: ({
    selectRtrDailyChartData,
    selectRtrTendencyChartData,
  }) => ({
    selectRtrDailyChatJsBinding: createSelector(
      adminFeature.selectActiveLocation,
      selectRtrDailyChartData,
      (activeLocation, chartData) =>
        filterRtrChartDataByActiveLocation(chartData, activeLocation),
    ),
    selectRtrTendencyChatJsBinding: createSelector(
      adminFeature.selectActiveLocation,
      selectRtrTendencyChartData,
      (activeLocation, chartData) =>
        filterRtrChartDataByActiveLocation(chartData, activeLocation),
    ),
  }),
});

function filterRtrChartDataByActiveLocation(
  chartData: IRTRChartData,
  activeLocation: ISigrowLocation | undefined,
) {
  const jsChartDataBinding = cloneDeep(chartData);
  jsChartDataBinding.data.datasets = jsChartDataBinding.data.datasets.filter(
    (ds) =>
      activeLocation?.location?.cameras.some(
        (c) => c.thermal_camera_id === (ds as CameraDataset).cameraId,
      ),
  );
  return jsChartDataBinding;
}
