/* eslint-disable @typescript-eslint/no-explicit-any */

import { Injectable } from '@angular/core';
import { ChartConfiguration } from 'chart.js';
import { AnnotationOptions } from 'chartjs-plugin-annotation';
import dayjs from 'dayjs';
import regression from 'regression';
import { lastValueFrom } from 'rxjs';
import { BaseDashboardService } from '../../../../common/base.dashboard.service';
import { ISigrowLocation } from '../../../../model/admin';
import { CameraDataset, IRTRChartData } from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';
import { ColorUtils } from '../../../../utils/color';

@Injectable({
  providedIn: 'root',
})
export class RtrService extends BaseDashboardService {
  async calculateRTR(
    location: ISigrowLocation,
    dateRange: DateRange,
  ): Promise<
    { rtrDaily: IRTRChartData; rtrTendency: IRTRChartData } | undefined
  > {
    const apiDateRange = this.getApiDateRange(dateRange, 'YYYYMMDD');

    const rtrData = (
      await lastValueFrom(
        this.cameraApi.camRtrDataRetrieve(
          apiDateRange.date_begin,
          apiDateRange.date_end,
        ),
      )
    ).data;

    if (!rtrData.length) {
      return;
    }

    const tempBase = 10;

    const labels = new Set<string>();

    const rtrDailyDatasets: CameraDataset[] = [];
    const rtrDailyOptions: ChartConfiguration['options'] = {
      animation: {
        duration: 0,
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 2.5,
          min: 0,
        },
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: (tooltipItem) => {
              const values = tooltipItem.raw as number[];
              return `RTR Range: ${values[1].toFixed(2)}-${values[0].toFixed(2)}`;
            },
            // afterBody: (tooltipItems) => {
            //   return [
            //     `↑ DLI = 10.2 mol/m2`,
            //     `↓ AVG Flower Temp: 19.4°C`,
            //     `↓ AVG Leave Temp: 18.8°C`,
            //   ];
            // },
          },
        },
      },
    };

    const rtrTendencyDatasets: CameraDataset[] = [];
    const rtrTendencyOptions: ChartConfiguration['options'] = {
      animation: {
        duration: 0,
      },
    };

    for (const rtrCamData of rtrData) {
      if (!rtrCamData.camera_data.length) {
        continue;
      }

      const dailyFlowerDataset: CameraDataset = {
        label: `${rtrCamData.camera_id.toString()} flowers`,
        data: [],
        cameraId: rtrCamData.camera_id,
      };

      const dailyLeafDataset: CameraDataset = {
        label: `${rtrCamData.camera_id.toString()} leafs`,
        data: [],

        cameraId: rtrCamData.camera_id,
      };

      const tendencyMinDataset: CameraDataset = {
        label: `${rtrCamData.camera_id.toString()} Min Temp`,
        data: [],
        cameraId: rtrCamData.camera_id,
      };

      const tendencyMaxDataset: CameraDataset = {
        label: `${rtrCamData.camera_id.toString()} Max Temp`,
        data: [],
        cameraId: rtrCamData.camera_id,
      };

      for (const rtrCamDataDay of rtrCamData.camera_data) {
        if (!rtrCamDataDay.date) {
          return;
        }

        const rtrFlowerMax = this.getRtrValue(
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_min_flower_temperature,
          tempBase,
        );
        const rtrFlowerMin = this.getRtrValue(
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_max_flower_temperature,
          tempBase,
        );
        const rtrLeaveMax = this.getRtrValue(
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_min_leaf_temperature,
          tempBase,
        );
        const rtrLeaveMin = this.getRtrValue(
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_max_leaf_temperature,
          tempBase,
        );

        dailyFlowerDataset.data.push([rtrFlowerMax ?? 0, rtrFlowerMin ?? 0]);
        dailyLeafDataset.data.push([rtrLeaveMax ?? 0, rtrLeaveMin ?? 0]);

        tendencyMinDataset.data.push([
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_min_flower_temperature,
        ]);
        tendencyMaxDataset.data.push([
          rtrCamDataDay.avg_dli,
          rtrCamDataDay.avg_max_flower_temperature,
        ]);

        labels.add(dayjs(rtrCamDataDay.date).format('DD MMMM'));
      }

      const linearRegression = regression.linear(
        tendencyMinDataset.data as [number, number][],
      );

      const linearRegressionDataset: CameraDataset = {
        cameraId: rtrCamData.camera_id,
        type: 'line',
        label: `${rtrCamData.camera_id.toString()} LinReg`,
        data: linearRegression.points.map((p) => ({ x: p[0], y: p[1] })),
        backgroundColor: '#2196F3',
        borderColor: '#2196F3',
        borderWidth: 2,
      };

      rtrDailyDatasets.push(dailyFlowerDataset, dailyLeafDataset);

      rtrTendencyDatasets.push(
        tendencyMinDataset,
        tendencyMaxDataset,
        linearRegressionDataset,
      );
    }

    try {
      const ranges = [
        {
          start: 0,
          end: 1.4,
          color: ColorUtils.addAlpha('#E3F2FD', 0.4),
        },
        {
          start: 1.7,
          end: 2.5,
          color: ColorUtils.addAlpha('#FFEBEE', 0.4),
        },
      ];
      rtrDailyOptions!.plugins!.annotation = {
        annotations: ranges.map(
          (range) =>
            ({
              drawTime: 'beforeDatasetsDraw',
              type: 'box',
              yMin: range.start,
              yMax: range.end,
              borderColor: 'transparent',
              backgroundColor: range.color,
            }) satisfies AnnotationOptions,
        ),
      };
    } catch (err) {
      console.error(err);
    }

    // TODO: fix this
    return {
      rtrDaily: {
        data: {
          labels: Array.from(labels),
          datasets: rtrDailyDatasets,
        },
        options: rtrDailyOptions,
      },
      rtrTendency: {
        data: {
          labels: Array.from(labels),
          datasets: rtrTendencyDatasets,
        },
        options: rtrTendencyOptions,
      },
    };
  }

  private getRtrValue(dli: number, temp: number, tempBase: number) {
    const tempDiff = temp - tempBase;
    return tempDiff ? dli / tempDiff : undefined;
  }
}
