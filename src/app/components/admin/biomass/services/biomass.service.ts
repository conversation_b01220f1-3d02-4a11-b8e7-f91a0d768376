import { Injectable } from '@angular/core';
import { Point } from 'chart.js';
import dayjs from 'dayjs';
import { lastValueFrom } from 'rxjs';
import {
  FlowersDeltaHistoryPostRequest,
  FlowersDeltaPostRequest,
  IFlowersDeltaPostRequest,
} from '../../../../api/api-sdk';
import { BaseDashboardService } from '../../../../common/base.dashboard.service';
import { ISigrowLocation } from '../../../../model/admin';
import {
  IChartData,
  IDevice,
  SigrowDataset,
} from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';
import { BIOMASS_MSG_ID } from '../../../../utils/device';

@Injectable({
  providedIn: 'root',
})
export class BiomassService extends BaseDashboardService {
  async calculateBiomass(
    activeLocation: ISigrowLocation,
    devices: IDevice[],
    dateRange: DateRange,
  ): Promise<{ biomassChart: IChartData } | undefined> {
    const apiDateRange = this.getApiDateRange(dateRange, 'YYYYMMDD');

    const data = this.getEmptyChartData();
    const options = this.getEmptyChartOptions(dateRange);
    options!.interaction!.mode = 'nearest';
    options!.plugins!.tooltip!.enabled = true;
    options!.plugins!.legend!.display = true;

    for (const remote of activeLocation.location!.remotes.filter(
      (r) => r.msg_id === BIOMASS_MSG_ID,
    )) {
      try {
        const biomassData = await lastValueFrom(
          this.dataApi.biomassGainUpdatedRetrieve(
            activeLocation.location!.central_id,
            apiDateRange.date_begin,
            apiDateRange.date_end,
            remote.remote_id,
          ),
        );

        for (const valuePath of ['biomass_val', 'lightsum_val']) {
          const valueChartData = biomassData.data
            .map((d) => d.day_data)
            .flat()
            .map(
              (d) =>
                ({
                  x: dayjs(d.date, 'YYYY-MM-DD HH:mm').valueOf(),
                  y: d[valuePath],
                }) as Point,
            );

          if (!valueChartData.length) {
            continue;
          }

          const dataset: SigrowDataset = {
            data: valueChartData,
            label: `${remote.name} ${valuePath}`,
            pointRadius: 0,
            backgroundColor: 'transparent',
            deviceId: remote.remote_id,
            uom: valuePath,
            variableName: remote.name,
            yAxisID: valuePath,
          };
          this.assignDatasetColors(dataset);
          data.datasets.push(dataset);
        }
      } catch (err) {
        console.log(err);
      }
    }

    return {
      biomassChart: {
        data,
        options,
        metadata: undefined,
      },
    };
  }

  async calculateFlowersDelta(
    activeLocation: ISigrowLocation,
    request: IFlowersDeltaPostRequest,
  ) {
    const remote = this.getRemote(activeLocation);

    const calcRes = await lastValueFrom(
      this.dataApi.flowersDeltaCreate(
        activeLocation.location!.central_id,
        remote.remote_id,
        new FlowersDeltaPostRequest(request),
      ),
    );

    return calcRes;
  }

  async getHistoryItems(activeLocation: ISigrowLocation) {
    const remote = this.getRemote(activeLocation);

    return (
      await lastValueFrom(
        this.dataApi.flowersDeltaHistoryRetrieve(
          activeLocation.location!.central_id,
          remote.remote_id,
        ),
      )
    ).items;
  }

  async saveHistoryItem(
    activeLocation: ISigrowLocation,
    request: FlowersDeltaHistoryPostRequest,
  ) {
    const remote = this.getRemote(activeLocation);

    await lastValueFrom(
      this.dataApi.flowersDeltaHistoryCreate(
        activeLocation.location!.central_id,
        remote.remote_id,
        request,
      ),
    );
  }

  private getRemote(activeLocation: ISigrowLocation) {
    const remote = activeLocation.location!.remotes.find(
      (r) => r.msg_id === BIOMASS_MSG_ID,
    );

    if (!remote) {
      throw new Error(
        'Location has no devices applicable for the biomass flowers delta calcs',
      );
    }

    return remote;
  }
}
