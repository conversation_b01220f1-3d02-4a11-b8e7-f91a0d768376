import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import { isNumber, merge } from 'lodash';
import { lastValueFrom, take } from 'rxjs';
import {
  FlowersDeltaCalcItem,
  FlowersDeltaHistoryPostRequest,
  IFlowersDeltaPostResponse,
} from '../../../../../api/api-sdk';
import { BaseComponent } from '../../../../../common/base.component';
import { DpDateFormatPipe } from '../../../../../common/pipes/dayjs/dp-date-format.pipe';
import { DpFromUnixPipe } from '../../../../../common/pipes/dayjs/dp-from-unix.pipe';
import SharedModules from '../../../../../common/shared.modules';
import { adminFeature } from '../../../../../state/admin/feature';
import { BiomassService } from '../../services/biomass.service';

@Component({
  selector: 'app-flowers-delta',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    ReactiveFormsModule,
    DpDateFormatPipe,
    DpFromUnixPipe,
  ],
  templateUrl: './flowers-delta.component.html',
  styleUrl: './flowers-delta.component.scss',
})
export class FlowersDeltaComponent extends BaseComponent implements OnInit {
  flowersKeep: number | undefined;
  flowersRemove: number | undefined;

  calcRes: IFlowersDeltaPostResponse | undefined = undefined;

  history: FlowersDeltaCalcItem[] = [];

  form = new FormGroup({
    periodStart: new FormControl<Date>(dayjs().subtract(7, 'days').toDate(), {
      validators: [Validators.required],
      nonNullable: true,
    }),
    periodEnd: new FormControl<Date>(new Date(), {
      validators: [Validators.required],
      nonNullable: true,
    }),
    minFruitWeight: new FormControl<number>(260, {
      validators: [Validators.required],
      nonNullable: true,
    }),
    freshWeightDistribution: new FormControl<number>(85, {
      validators: [Validators.required],
      nonNullable: true,
    }),
    plantsPerM2: new FormControl<number>(3.56, {
      validators: [Validators.required],
      nonNullable: true,
    }),
    leavesLastPeriod: new FormControl<number | undefined>(undefined, {
      validators: [Validators.required],
      nonNullable: true,
    }),
    nextPeriodDLI: new FormControl<number | undefined>(undefined, {
      nonNullable: true,
    }),
  });

  // TODO: Convert direct service usage into store actions once calcs approved
  constructor(
    private store: Store,
    private biomassService: BiomassService,
  ) {
    super();
  }

  ngOnInit() {
    setTimeout(() => {
      this.loadHistory();
    }, 1000);
  }

  async calc() {
    if (!this.form.valid) {
      return;
    }

    const activeLocation = await this.getActiveLocation();

    this.calcRes = await this.biomassService.calculateFlowersDelta(
      activeLocation,
      this.getAPICalcInputParams(),
    );

    const flowersToRemove = this.calcRes.next_period_flowers_delta;
    const inputParams = this.form.value;
    if (isNumber(flowersToRemove) && flowersToRemove > 0) {
      this.flowersRemove = flowersToRemove;
      this.flowersKeep = inputParams.leavesLastPeriod! - this.flowersRemove;
    } else {
      this.flowersRemove = 0;
      this.flowersKeep = inputParams.leavesLastPeriod;
    }
  }

  async save() {
    if (!this.calcRes || !(await this.ui.confirm())) {
      return;
    }

    const activeLocation = await this.getActiveLocation();

    const request = new FlowersDeltaHistoryPostRequest();
    merge(request, this.getAPICalcInputParams(), this.calcRes);

    await this.biomassService.saveHistoryItem(activeLocation, request);
    await this.loadHistory();
  }

  private async loadHistory() {
    const activeLocation = await this.getActiveLocation();
    this.history = await this.biomassService.getHistoryItems(activeLocation);
  }

  private async getActiveLocation() {
    const activeLocation = await lastValueFrom(
      this.store.select(adminFeature.selectActiveLocation).pipe(take(1)),
    );

    if (!activeLocation) {
      throw new Error('Missing active location');
    }

    return activeLocation;
  }

  private getAPICalcInputParams() {
    const inputParams = this.form.value;
    return {
      period_start_timestamp: dayjs(inputParams.periodStart).unix(),
      period_end_timestamp: dayjs(inputParams.periodEnd).unix(),
      min_fruit_weight: inputParams.minFruitWeight!,
      fresh_weight_distribution: inputParams.freshWeightDistribution! / 100,
      plants_per_m2: inputParams.plantsPerM2!,
      last_period_new_leaves: inputParams.leavesLastPeriod!,
      next_period_dli: inputParams.nextPeriodDLI,
    };
  }
}
