<div class="w-full h-full p-6 flex flex-col">
  <div class="flex mb-6">
    <div
      class="flex-1 flex flex-col mr-6 p-6 pb-0 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
    >
      <h4 class="flex-none mb-6 text-xl font-bold text-black dark:text-white">
        Input parameters
      </h4>
      <ng-container [formGroup]="form">
        <div class="flex">
          <mat-form-field class="flex-1 mr-5">
            <mat-label>Last period date range</mat-label>
            <mat-date-range-input [rangePicker]="picker">
              <input
                matStartDate
                formControlName="periodStart"
                placeholder="Start date"
              />
              <input
                matEndDate
                formControlName="periodEnd"
                placeholder="End date"
              />
            </mat-date-range-input>
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker"
            ></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
          </mat-form-field>

          <mat-form-field class="flex-1 mr-5">
            <mat-label>Last period new leaves</mat-label>
            <input
              matInput
              formControlName="leavesLastPeriod"
              type="number"
              min="0"
            />
          </mat-form-field>

          <mat-form-field class="flex-1">
            <mat-label>Minimal fruit weight</mat-label>
            <input
              matInput
              formControlName="minFruitWeight"
              type="number"
              min="0"
            />
            <span matTextSuffix>grams</span>
          </mat-form-field>
        </div>

        <div class="flex">
          <mat-form-field class="flex-1 mr-5">
            <mat-label>Fresh weight distribution</mat-label>
            <input
              matInput
              formControlName="freshWeightDistribution"
              type="number"
              min="0"
            />
            <span matTextSuffix>%</span>
          </mat-form-field>

          <mat-form-field class="flex-1 mr-5">
            <mat-label>Number of plants per m2</mat-label>
            <input
              matInput
              formControlName="plantsPerM2"
              type="number"
              min="0"
            />
          </mat-form-field>

          <mat-form-field class="flex-1">
            <mat-label>Next period DLI (optional)</mat-label>
            <input
              matInput
              formControlName="nextPeriodDLI"
              type="number"
              min="0"
            />
            <span matTextSuffix>mol/m2</span>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
    <div
      class="flex-none w-100 h-full flex flex-col p-6 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
    >
      <h4 class="flex-none text-xl font-bold text-black dark:text-white">
        Current results
      </h4>
      <div class="flex-1 flex flex-col items-center justify-center">
        <div
          class="w-full text-center text-3xl mb-3 bg-green-100 text-green-800 font-medium me-2 px-2.5 py-0.5 rounded"
        >
          Keep: {{ flowersKeep ?? "-" }}
        </div>
        <div
          class="w-full text-center text-3xl bg-pink-100 text-pink-800 font-medium me-2 px-2.5 py-0.5 rounded"
        >
          Remove: {{ flowersRemove ?? "-" }}
        </div>
      </div>
      <div class="flex-none flex">
        <button
          mat-button
          [disabled]="!form.valid"
          (click)="calc()"
          class="!bg-indigo-50"
        >
          Calculate
        </button>
        <span class="flex-1"></span>
        <button
          mat-button
          [disabled]="!calcRes"
          (click)="save()"
          class="!bg-indigo-50"
        >
          Save
        </button>
      </div>
    </div>
  </div>
  <div
    class="p-6 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark"
  >
    <h4 class="mb-6 text-xl font-bold text-black dark:text-white">
      Calculations history
    </h4>
    <div class="flex flex-col">
      <div
        class="grid grid-cols-3 rounded-sm bg-gray-2 dark:bg-meta-4 sm:grid-cols-5"
      >
        <div class="p-2.5 xl:p-5">
          <h5 class="text-sm font-medium uppercase xsm:text-base">Period</h5>
        </div>
        <div class="p-2.5 text-center xl:p-5">
          <h5 class="text-sm font-medium uppercase xsm:text-base">
            Prev new leaves
          </h5>
        </div>
        <div class="p-2.5 text-center xl:p-5">
          <h5 class="text-sm font-medium uppercase xsm:text-base">
            Flowers delta
          </h5>
        </div>
        <div class="hidden p-2.5 text-center sm:block xl:p-5">
          <h5 class="text-sm font-medium uppercase xsm:text-base">
            Next biomass
          </h5>
        </div>
        <div class="hidden p-2.5 text-center sm:block xl:p-5">
          <h5 class="text-sm font-medium uppercase xsm:text-base">
            Next biomass/plant
          </h5>
        </div>
      </div>

      @for (historyItem of history; track historyItem) {
        <div
          class="grid grid-cols-3 border-b border-stroke dark:border-strokedark sm:grid-cols-5"
        >
          <div class="flex items-center gap-3 p-2.5 xl:p-5">
            <p class="hidden font-medium text-black dark:text-white sm:block">
              {{
                historyItem.period_start_timestamp
                  | dpFromUnix
                  | dpDateFormat: "DD/MM/YYYY"
              }}
              -
              {{
                historyItem.period_end_timestamp
                  | dpFromUnix
                  | dpDateFormat: "DD/MM/YYYY"
              }}
            </p>
          </div>

          <div class="flex items-center justify-center p-2.5 xl:p-5">
            <p class="font-medium text-black dark:text-white">
              {{ historyItem.last_period_new_leaves }}
            </p>
          </div>

          <div class="flex items-center justify-center p-2.5 xl:p-5">
            <p
              [ngClass]="{
                'text-meta-3': historyItem.next_period_flowers_delta > 0,
                'text-meta-1': historyItem.next_period_flowers_delta < 0
              }"
              class="font-medium"
            >
              {{ historyItem.next_period_flowers_delta }}
            </p>
          </div>

          <div class="flex items-center justify-center p-2.5 xl:p-5">
            <p class="font-medium">
              {{ historyItem.next_period_fruits_biomass }}
            </p>
          </div>

          <div class="hidden items-center justify-center p-2.5 sm:flex xl:p-5">
            <p class="font-medium text-black dark:text-white">
              {{ historyItem.next_period_fruits_biomass_per_plant }}
            </p>
          </div>
        </div>
      }
    </div>
  </div>
</div>
