import { CommonModule } from '@angular/common';
import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { Observable, map } from 'rxjs';
import { BaseComponent } from '../../../../../common/base.component';
import SharedModules from '../../../../../common/shared.modules';
import { IChartData, SigrowDataset } from '../../../../../model/dashboard';
import { DateRange } from '../../../../../model/dateRange';
import { DateRangePickerComponent } from '../../../../common/date-range-picker/date-range-picker.component';
import { BiomassActions } from '../../state/biomass.actions';
import { biomassFeature } from '../../state/biomass.feature';

@Component({
  selector: 'app-biomass',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    NgChartsModule,
    DateRangePickerComponent,
    LetDirective,
  ],
  templateUrl: './biomass.component.html',
  styleUrl: './biomass.component.scss',
})
export class BiomassComponent extends BaseComponent implements OnInit {
  @ViewChildren(BaseChartDirective) charts?: QueryList<BaseChartDirective>;

  chartData$!: Observable<IChartData>;
  dateRange$!: Observable<DateRange>;
  cameraIds$!: Observable<string[]>;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.chartData$ = this.store.select(
      biomassFeature.selectBiomassChartJsBinding,
    );
    this.dateRange$ = this.store.select(biomassFeature.selectDateRange);
    this.cameraIds$ = this.chartData$.pipe(
      map((chartData) =>
        Array.from(
          new Set([...chartData.data.datasets.map((ds) => ds.variableName)]),
        ),
      ),
    );
  }

  dateRangeChanged(dateRange: DateRange) {
    this.store.dispatch(BiomassActions.dateRangeChanged({ dateRange }));
  }

  // TODO: make this code shared
  toggleCamera(cameraId: string) {
    for (const chart of this.charts?.toArray() ?? []) {
      const cameraDatasets = chart.data?.datasets?.filter(
        (ds) => (ds as SigrowDataset).variableName === cameraId,
      );
      for (const cameraDS of cameraDatasets ?? []) {
        cameraDS.hidden = !cameraDS.hidden;
      }
      chart.update();
    }
  }
}
