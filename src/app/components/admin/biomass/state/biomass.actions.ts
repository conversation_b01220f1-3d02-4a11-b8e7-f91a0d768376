import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { IChartData } from '../../../../model/dashboard';
import { DateRange } from '../../../../model/dateRange';

export const BiomassActions = createActionGroup({
  source: 'Biomass',
  events: {
    dateRangeChanged: props<{ dateRange: DateRange }>(),
    regenerateChart: emptyProps(),
    regenerateChartSuccess: props<{
      biomassChart: IChartData;
    }>(),
  },
});
