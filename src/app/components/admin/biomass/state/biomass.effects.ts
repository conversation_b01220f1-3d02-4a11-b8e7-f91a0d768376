import { Injectable } from '@angular/core';
import { Actions, OnInitEffects, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { exhaustMap, filter, map } from 'rxjs';

import { AdminActions } from '../../../../state/admin/actions';
import { adminFeature } from '../../../../state/admin/feature';
import { BiomassService } from '../services/biomass.service';
import { BiomassActions } from './biomass.actions';
import { biomassFeature } from './biomass.feature';

@Injectable()
export class BiomassEffects implements OnInitEffects {
  regenerateChart$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        AdminActions.activeLocationConfigRetrived,
        BiomassActions.dateRangeChanged,
        BiomassActions.regenerateChart,
      ),
      concatLatestFrom(() => [
        this.store.select(adminFeature.selectActiveLocation),
        this.store.select(adminFeature.selectAllDevicesForActiveLocation),
        this.store.select(biomassFeature.selectDateRange),
      ]),
      filter((values) => values.every((v) => !!v)),
      exhaustMap(([, activeLocation, devices, dateRange]) =>
        this.biomassMng.calculateBiomass(activeLocation!, devices, dateRange),
      ),
      filter((chartData) => !!chartData),
      map((chartData) =>
        BiomassActions.regenerateChartSuccess({
          biomassChart: chartData!.biomassChart,
        }),
      ),
    );
  });

  ngrxOnInitEffects() {
    return BiomassActions.regenerateChart();
  }

  constructor(
    private store: Store,
    private actions$: Actions,
    private biomassMng: BiomassService,
  ) {}
}
