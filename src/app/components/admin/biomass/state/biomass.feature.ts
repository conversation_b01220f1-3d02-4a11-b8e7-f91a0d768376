/* eslint-disable @typescript-eslint/no-explicit-any */

import { createFeature, createReducer, createSelector, on } from '@ngrx/store';
import { produce } from 'immer';
import { cloneDeep } from 'lodash';
import { IChartData } from '../../../../model/dashboard';
import {
  DateRange,
  DateRangesPredefined,
  dateRanges,
} from '../../../../model/dateRange';
import { BiomassActions } from './biomass.actions';

export interface IBiomassState {
  dateRange: DateRange;
  biomassChart: IChartData;
}

export const initialState: IBiomassState = {
  dateRange: dateRanges.find(
    (dr) => dr.name === DateRangesPredefined.last24hours,
  )!,
  biomassChart: {
    data: { labels: [], datasets: [] },
    options: {},
    metadata: undefined,
  },
};

export const reducer = createReducer(
  initialState,
  on(BiomassActions.regenerateChartSuccess, (state, action) =>
    produce(state, (draft) => {
      draft.biomassChart = action.biomassChart as any;
    }),
  ),
  on(BiomassActions.dateRangeChanged, (state, action) =>
    produce(state, (draft) => {
      draft.dateRange = action.dateRange;
    }),
  ),
);

// TODO: unify this type of states with RTR etc
export const biomassFeature = createFeature({
  name: 'Biomass',
  reducer,
  extraSelectors: ({ selectBiomassChart }) => ({
    selectBiomassChartJsBinding: createSelector(
      selectBiomassChart,
      (chartData) => cloneDeep(chartData),
    ),
  }),
});
