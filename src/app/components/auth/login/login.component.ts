import { CUSTOM_ELEMENTS_SCHEMA, Component, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import SharedModules from '../../../common/shared.modules';
import { AuthActions } from '../../../state/auth/actions';
import { BannersComponent } from '../../common/banners/banners.component';
import { authFeature } from './../../../state/auth/feature';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    ...SharedModules,
    BannersComponent,
    RouterModule,
    ReactiveFormsModule,
    LetDirective,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LoginComponent implements OnInit {
  images$!: Observable<string[]>;
  loginFailure$!: Observable<unknown>;

  form = new FormGroup({
    userName: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
    password: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
    rememberUser: new FormControl<boolean>(true, { nonNullable: true }),
  });

  constructor(private store: Store) {}

  ngOnInit() {
    this.images$ = this.store.select(authFeature.selectLoginImages);
    this.loginFailure$ = this.store.select(authFeature.selectLoginFailure);
    this.store.dispatch(AuthActions.fetchLoginImages());
  }

  login() {
    if (!this.form.valid) {
      return;
    }
    this.store.dispatch(
      AuthActions.loginWithCredentials({ ...this.form.getRawValue() }),
    );
  }
}
