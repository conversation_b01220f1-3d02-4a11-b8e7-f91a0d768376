<div
  class="mx-auto max-w-screen-2xl h-full flex items-center justify-center p-4 md:p-6 2xl:p-10"
>
  <app-banners></app-banners>

  <div
    class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark z-10"
  >
    <div class="flex flex-wrap items-center">
      <div class="w-full border-stroke dark:border-strokedark">
        <div class="w-full p-4 sm:p-12.5">
          <img
            src="assets/img/banner_10.svg"
            alt="10 years"
            class="w-125 mb-7"
          />
          <h2
            class="mb-9 text-2xl font-bold text-black dark:text-white sm:text-title-xl2"
          >
            Sign In to Sigrow
          </h2>

          <div [formGroup]="form">
            <mat-form-field class="w-full">
              <mat-label>User name</mat-label>
              <input formControlName="userName" matInput />
            </mat-form-field>

            <mat-form-field class="w-full">
              <mat-label>Password</mat-label>
              <input formControlName="password" matInput type="password" />
            </mat-form-field>

            <ng-container *ngrxLet="loginFailure$ as loginFailure">
              @if (loginFailure !== undefined) {
                <div class="mb-5 text-red">
                  {{ "emailPassIncor" | translate }}
                </div>
              }
            </ng-container>

            <div class="mb-5">
              <mat-checkbox formControlName="rememberUser" color="primary">
                <span class="text-base"> Remember me </span>
              </mat-checkbox>
            </div>

            <div class="mb-5">
              <input
                type="submit"
                value="Sign In"
                [disabled]="!form.valid"
                (click)="login()"
                class="w-full cursor-pointer rounded-lg bg-primary p-4 font-medium text-white"
              />
            </div>

            <div class="mt-6 text-center">
              <p class="font-medium">
                Forgot user name or password?
                <a [routerLink]="['/auth/reset']" class="text-primary"
                  >Restore account</a
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-container *ngrxLet="images$ as images">
  @if (images?.length) {
    <swiper-container
      speed="1000"
      autoplay-delay="5000"
      class="fixed top-0 left-0 w-full h-full"
    >
      @for (image of images; track image) {
        <swiper-slide>
          <div
            [style.background-image]="'url(' + image + ')'"
            class="w-full h-full bg-no-repeat bg-cover bg-center"
          ></div>
        </swiper-slide>
      }
    </swiper-container>
  }
</ng-container>
