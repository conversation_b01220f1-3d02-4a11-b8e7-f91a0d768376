import { Component } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import SharedModules from '../../../common/shared.modules';
import { AuthActions } from '../../../state/auth/actions';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [...SharedModules, RouterModule, ReactiveFormsModule],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss',
})
export class ResetPasswordComponent {
  form = new FormGroup({
    email: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required, Validators.email],
    }),
  });

  constructor(private store: Store) {}

  resetPassword() {
    if (!this.form.valid) {
      return;
    }
    this.store.dispatch(
      AuthActions.resetPassword({ email: this.form.controls.email.value }),
    );
  }
}
