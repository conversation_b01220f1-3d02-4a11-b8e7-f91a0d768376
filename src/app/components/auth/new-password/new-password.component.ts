import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import SharedModules from '../../../common/shared.modules';
import { AuthActions } from './../../../state/auth/actions';

@Component({
  selector: 'app-new-password',
  standalone: true,
  imports: [...SharedModules, RouterModule, ReactiveFormsModule],
  templateUrl: './new-password.component.html',
  styleUrl: './new-password.component.scss',
})
export class NewPasswordComponent implements OnInit {
  form = new FormGroup({
    email: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required, Validators.email],
    }),
    password: new FormControl<string>('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
    confirmPassword: new FormControl<string>('', {
      nonNullable: true,
      validators: [
        Validators.required,
        ConfirmPasswordValidator.matchPasswords,
      ],
    }),
  });

  private token!: string;

  constructor(
    private store: Store,
    private route: ActivatedRoute,
  ) {}

  ngOnInit() {
    this.token = this.route.snapshot.queryParams['token'];
    if (!this.token) {
      this.form.disable();
    }
  }

  setNewPassword() {
    if (!this.form.valid) {
      return;
    }
    this.store.dispatch(
      AuthActions.setNewPassword({
        token: this.token,
        email: this.form.controls.email.value,
        password: this.form.controls.password.value,
      }),
    );
  }
}

export class ConfirmPasswordValidator {
  static matchPasswords(control: AbstractControl): ValidationErrors | null {
    const form = control.parent;
    const password = form?.get('password')?.value;
    const confirmPassword = form?.get('confirmPassword')?.value;
    return password !== confirmPassword ? { ConfirmPassword: true } : null;
  }
}
