<div
  class="mx-auto max-w-screen-2xl h-full flex items-center justify-center p-4 md:p-6 2xl:p-10"
>
  <div
    class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark z-10"
  >
    <div class="flex flex-wrap items-center">
      <div class="w-full border-stroke dark:border-strokedark">
        <div class="w-115 p-4 sm:p-12.5">
          <h2
            class="mb-9 text-2xl font-bold text-black dark:text-white sm:text-title-xl2"
          >
            Enter new password
          </h2>

          <div [formGroup]="form" class="w-72">
            <mat-form-field class="w-full">
              <mat-label>Email</mat-label>
              <input formControlName="email" matInput />
            </mat-form-field>

            <mat-form-field class="w-full">
              <mat-label>Password</mat-label>
              <input formControlName="password" matInput type="password" />
            </mat-form-field>

            <mat-form-field class="w-full">
              <mat-label>Confirm password</mat-label>
              <input
                formControlName="confirmPassword"
                matInput
                type="password"
              />
            </mat-form-field>

            <button
              mat-flat-button
              color="primary"
              [disabled]="!form.valid"
              (click)="setNewPassword()"
              class="w-full"
            >
              Set new password
            </button>

            <div class="w-full text-center mt-8">
              <a
                [routerLink]="['/auth/login']"
                class="text-blue-600 hover:underline"
                >Back to login</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
