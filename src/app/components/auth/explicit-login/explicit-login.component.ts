import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { omit } from 'lodash';
import { AuthActions } from '../../../state/auth/actions';

@Component({
  selector: 'app-explicit-login',
  standalone: true,
  imports: [],
  template: '',
  styleUrls: [],
})
export class ExplicitLoginComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private store: Store,
  ) {}

  ngOnInit() {
    const queryParams = this.route.snapshot.queryParams;
    const token = queryParams['token'];

    if (!token) {
      throw new Error('Missing explicit login required parameters');
    }

    const returnUrl = queryParams['returnUrl'];
    const params = omit(queryParams, 'token', 'returnUrl');

    this.store.dispatch(
      AuthActions.explicitLogin({
        token,
        returnUrl,
        params,
      }),
    );
  }
}
