import { Route } from '@angular/router';
import { ExplicitLoginComponent } from './explicit-login/explicit-login.component';
import { LoginComponent } from './login/login.component';
import { NewPasswordComponent } from './new-password/new-password.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';

export const AUTH_ROUTES: Route[] = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'login',
  },
  {
    path: 'login',
    component: LoginComponent,
  },
  {
    path: 'reset',
    component: ResetPasswordComponent,
  },
  {
    path: 'newPassword',
    component: NewPasswordComponent,
  },
  {
    path: 'explicitLogin',
    component: ExplicitLoginComponent,
  },
];
