import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NgxGaugeModule } from 'ngx-gauge';

@Component({
  selector: 'app-gauge',
  templateUrl: './gauge.component.html',
  styleUrls: ['./gauge.component.scss'],
  standalone: true,
  imports: [CommonModule, NgxGaugeModule, TranslateModule],
})
export class GaugeComponent implements OnChanges {
  @Input() variable!: string;
  @Input() rangeMin!: number;
  @Input() rangeMax!: number;
  @Input() valueMin!: number;
  @Input() valueMax!: number;
  @Input() value!: number;
  @Input() uom?: string;

  markerConfig: { [key: string]: any } = {};
  thresholdConfig: { [key: string]: any } = {};

  ngOnChanges(changes: SimpleChanges) {
    this.updateConfigurations();
  }

  private updateConfigurations() {
    // Update marker configuration
    this.markerConfig = {
      [this.valueMin.toString()]: {
        color: '#777',
        size: 6,
        label: 'min',
        type: 'line',
      },
      [this.valueMax.toString()]: {
        color: '#777',
        size: 6,
        label: 'max',
        type: 'line',
      },
    };

    // Update threshold configuration
    this.thresholdConfig = {
      0: {
        color: '#0091db',
        bgOpacity: 0.2,
      },
      [this.valueMin]: {
        color: '#2ba581',
        bgOpacity: 0.2,
      },
      [this.valueMax]: {
        color: '#EF5350',
        bgOpacity: 0.2,
      },
    };
  }
}
