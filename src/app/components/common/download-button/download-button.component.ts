import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { BaseComponent } from '../../../common/base.component';
import SharedModules from '../../../common/shared.modules';
import { ITask, TaskStatus } from '../../../model/task';
import { AdminActions } from './../../../state/admin/actions';
import { adminFeature } from './../../../state/admin/feature';

@Component({
  selector: 'app-download-button',
  standalone: true,
  imports: [...SharedModules, LetDirective],
  templateUrl: './download-button.component.html',
  styleUrl: './download-button.component.scss',
})
export class DownloadButtonComponent extends BaseComponent implements OnInit {
  @Input() toolbar = false;

  @Output() openDownloadDialog = new EventEmitter();

  task$!: Observable<ITask | undefined>;
  statuses = TaskStatus;

  constructor(private store: Store) {
    super();
  }

  ngOnInit(): void {
    this.task$ = this.store.select(adminFeature.selectExportTask);
  }

  startNewExport() {
    this.store.dispatch(AdminActions.deleteExportTask());
    this.openDownloadDialog.emit();
  }
}
