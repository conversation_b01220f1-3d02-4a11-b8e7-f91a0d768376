<ng-container *ngrxLet="task$ as task">
  @if (!task) {
    @if (toolbar) {
      <button
        mat-flat-button
        (click)="openDownloadDialog.emit()"
        [matTooltip]="'export' | translate"
        class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
      >
        <div class="flex items-center justify-center text-gray-700">
          <mat-icon>download</mat-icon>
        </div>
      </button>
    } @else {
      <button
        mat-mini-fab
        (click)="openDownloadDialog.emit()"
        [matTooltip]="'export' | translate"
        matTooltipPosition="left"
        color="light"
      >
        <mat-icon>download</mat-icon>
      </button>
    }
  } @else {
    @if (toolbar) {
      <button
        mat-flat-button
        [matMenuTriggerFor]="downloadMenu"
        class="mr-4 !px-2 !min-w-fit !bg-stroke z-1"
      >
        <div class="flex items-center justify-center text-gray-700">
          @switch (task.state?.status) {
            @case (statuses.completed) {
              <mat-icon class="text-green-600 filled">check_circle</mat-icon>
            }
            @case (statuses.failed) {
              <mat-icon class="text-red-600 filled">error</mat-icon>
            }
            @default {
              <mat-spinner class="!w-6 !h-6"></mat-spinner>
            }
          }
        </div>
      </button>
    } @else {
      <button mat-mini-fab [matMenuTriggerFor]="downloadMenu" color="light">
        @switch (task.state?.status) {
          @case (statuses.completed) {
            <mat-icon class="text-green-600 filled">check_circle</mat-icon>
          }
          @case (statuses.failed) {
            <mat-icon class="text-red-600 filled">error</mat-icon>
          }
          @default {
            <mat-spinner class="!w-6 !h-6"></mat-spinner>
          }
        }
      </button>
    }

    <mat-menu
      #downloadMenu="matMenu"
      xPosition="before"
      [yPosition]="toolbar ? 'below' : 'above'"
    >
      @if (task.state?.status === statuses.completed) {
        <a mat-menu-item [href]="task.state?.result?.url">
          {{ "downloadAsCSV" | translate }}
        </a>
      }
      <button mat-menu-item (click)="startNewExport()">
        {{ "createAnotherExport" | translate }}
      </button>
    </mat-menu>
  }
</ng-container>
