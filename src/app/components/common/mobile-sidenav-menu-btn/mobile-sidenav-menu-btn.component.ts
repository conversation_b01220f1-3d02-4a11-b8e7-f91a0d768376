import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenav } from '@angular/material/sidenav';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable, map } from 'rxjs';
import { BaseComponent } from '../../../common/base.component';
import { adminFeature } from '../../../state/admin/feature';
import { MobileBannerParameters } from '../../../utils/banner';

@Component({
  selector: 'app-mobile-sidenav-menu-btn',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, LetDirective],
  templateUrl: './mobile-sidenav-menu-btn.component.html',
  styleUrls: [],
})
export class MobileSidenavMenuBtnComponent
  extends BaseComponent
  implements OnInit
{
  @Input() sidenav!: MatSidenav;

  offsetTop$!: Observable<number>;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.offsetTop$ = this.store
      .select(adminFeature.selectBannersToDisplay)
      .pipe(
        map(
          (banners) => (banners?.length ?? 0) * MobileBannerParameters.height,
        ),
      );
  }
}
