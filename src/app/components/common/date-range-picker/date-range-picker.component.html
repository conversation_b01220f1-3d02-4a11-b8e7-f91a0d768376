<ng-container *ngrxLet="dateRange$ as dateRange">
  @if (showShiftControls) {
    <button
      mat-icon-button
      (click)="onShiftDateRange(-1)"
      class="!bg-slate-50 !z-1 !p-1.5 !w-[36px] !h-[36px] mr-2"
    >
      <mat-icon>arrow_back</mat-icon>
    </button>
  }
  <button
    mat-flat-button
    [matMenuTriggerFor]="dateRangeMenu"
    class="!bg-stroke z-1"
  >
    <div class="flex items-center justify-center text-gray-700">
      <mat-icon class="mr-3">date_range</mat-icon>
      {{ dateRange?.name ?? "selectDateRange" | translate }}
    </div>
  </button>
  @if (showShiftControls) {
    <button
      mat-icon-button
      (click)="onShiftDateRange(1)"
      class="!bg-slate-50 !z-1 !p-1.5 !w-[36px] !h-[36px] ml-2"
    >
      <mat-icon>arrow_forward</mat-icon>
    </button>
  }
</ng-container>
<mat-menu #dateRangeMenu="matMenu">
  @for (dateRange of dateRanges; track dateRange) {
    <button mat-menu-item (click)="onDateRangeChanged(dateRange)">
      {{ dateRange.name | translate }}
    </button>
  }
  <button mat-menu-item (click)="rangePicker.open()">
    {{ "selectDateRange" | translate }}
  </button>
  <div class="opacity-0 h-0 overflow-hidden">
    <mat-date-range-input
      [formGroup]="rangeForm"
      [rangePicker]="rangePicker"
      [dateFilter]="rangeDateFilter"
    >
      <input matStartDate formControlName="start" placeholder="Start date" />
      <input matEndDate formControlName="end" placeholder="End date" />
    </mat-date-range-input>
  </div>
  <mat-date-range-picker #rangePicker yPosition="above"></mat-date-range-picker>
</mat-menu>
