import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { DateFilterFn, MatDateRangePicker } from '@angular/material/datepicker';
import { LetDirective } from '@ngrx/component';
import dayjs from 'dayjs';
import { Observable, filter } from 'rxjs';
import { BaseComponent } from '../../../common/base.component';
import SharedModules from '../../../common/shared.modules';
import { DateRange, dateRanges } from '../../../model/dateRange';

@Component({
  selector: 'app-date-range-picker',
  standalone: true,
  imports: [...SharedModules, FormsModule, ReactiveFormsModule, LetDirective],
  templateUrl: './date-range-picker.component.html',
  styleUrl: 'date-range-picker.component.scss',
})
export class DateRangePickerComponent extends BaseComponent implements OnInit {
  @ViewChild(MatDateRangePicker) rangePicker!: MatDateRangePicker<Date>;

  @Input() dateRange$!: Observable<DateRange>;
  @Input() showShiftControls?: boolean;

  @Output() dateRangeChanged = new EventEmitter<DateRange>();
  @Output() shiftDateRange = new EventEmitter<number>();

  rangeForm = new FormGroup({
    start: new FormControl<Date | undefined>(undefined),
    end: new FormControl<Date | undefined>(undefined),
  });
  rangeDateFilter: DateFilterFn<Date> = (date: Date | null) => {
    return !!date && dayjs(date).isBefore(dayjs().endOf('day'));
  };

  dateRanges = dateRanges;

  ngOnInit() {
    this.subSafe(
      this.rangeForm.valueChanges.pipe(filter((v) => !!v.start && !!v.end)),
      (value: { start: Date; end: Date }) => {
        const start = dayjs(value.start).startOf('day');
        const end = dayjs(value.end).add(1, 'day').startOf('day');
        const dateRange = DateRange.fromStartEnd(start, end);
        dateRange.applyStartTimezone = false;
        dateRange.applyEndTimezone = false;
        this.onDateRangeChanged(dateRange);
        this.rangeForm.reset(undefined, { emitEvent: false });
        this.rangePicker.close();
      },
    );
  }

  onDateRangeChanged(dateRange: DateRange) {
    this.dateRangeChanged.emit(dateRange);
  }

  onShiftDateRange(delta: number) {
    this.shiftDateRange.emit(delta);
  }
}
