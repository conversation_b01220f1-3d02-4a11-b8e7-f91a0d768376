<ng-container
  *ngrxLet="{
    banners: banners$,
    bannerParams: ui.bannerParams$,
    userSettings: userSettings$
  } as state"
>
  @for (banner of state.banners; track banner) {
    <div
      [style.height.px]="state.bannerParams.height"
      [ngClass]="{
        'bg-[#285172]': banner.severity === bannerSeverity.INFO,
        'bg-danger': banner.severity === bannerSeverity.ERROR,
        'bg-warning': banner.severity === bannerSeverity.WARNING,
        'bg-[#2ba581]': banner.severity === bannerSeverity.SUCCESS,
        'cursor-pointer': banner.url
      }"
      class="flex items-center text-white border-b border-stroke ps-4 pe-2"
      (click)="handleBannerClick(banner, $event)"
    >
      @switch (banner.severity) {
        @case (bannerSeverity.ERROR) {
          <mat-icon>error</mat-icon>
        }
        @case (bannerSeverity.WARNING) {
          <mat-icon>warning</mat-icon>
        }
        @case (bannerSeverity.INFO) {
          <mat-icon>info</mat-icon>
        }
        @case (bannerSeverity.SUCCESS) {
          <mat-icon>check_circle</mat-icon>
        }
      }
      <div
        [style.max-height.px]="state.bannerParams.height"
        class="leading-5 flex-1 px-3 overflow-y-auto"
      >
        {{
          state.userSettings.language === "es" && banner.message_es
            ? banner.message_es
            : state.userSettings.language === "nl" && banner.message_nl
              ? banner.message_nl
              : banner.message
        }}
      </div>
      @if (banner.hideable || !banner.end_date) {
        <button mat-icon-button (click)="closeBanner(banner)">
          <mat-icon>close</mat-icon>
        </button>
      }
    </div>
  }
</ng-container>
