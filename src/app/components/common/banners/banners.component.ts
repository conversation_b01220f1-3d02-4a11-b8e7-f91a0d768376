import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { IBanner, IUserSettingsGet, SeverityEnum } from '../../../api/api-sdk';
import { BaseComponent } from '../../../common/base.component';
import { AdminActions } from '../../../state/admin/actions';
import { adminFeature } from '../../../state/admin/feature';

@Component({
  selector: 'app-banners',
  standalone: true,
  imports: [CommonModule, MatIconModule, LetDirective],
  templateUrl: './banners.component.html',
  styleUrl: './banners.component.scss',
})
export class BannersComponent extends BaseComponent implements OnInit {
  banners$!: Observable<IBanner[] | undefined>;
  userSettings$!: Observable<IUserSettingsGet>;

  bannerSeverity = SeverityEnum;

  constructor(private store: Store) {
    super();
  }

  ngOnInit() {
    this.banners$ = this.store.select(adminFeature.selectBannersToDisplay);
    this.userSettings$ = this.store.select(adminFeature.selectUserSettings);
  }

  closeBanner(banner: IBanner) {
    this.store.dispatch(AdminActions.bannerClosed({ banner }));
  }

  handleBannerClick(banner: IBanner, event: MouseEvent) {
    if (!banner.url) return;

    // Don't handle click if it was on the close button
    const target = event.target as HTMLElement;
    if (target.closest('button')) return;

    window.open(banner.url, '_blank');

    this.store.dispatch(AdminActions.bannerClicked({ banner }));
  }
}
