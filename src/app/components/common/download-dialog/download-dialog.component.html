<button mat-icon-button mat-dialog-close class="!absolute right-2 top-2 !z-10">
  <mat-icon>close</mat-icon>
</button>
<h2 mat-dialog-title>{{ "csvDataExport" | translate }}</h2>
<div mat-dialog-content class="!pb-0">
  <div class="flex flex-col">
    <div [formGroup]="form">
      <h3 class="mb-2">{{ "selectDateRange" | translate }}</h3>
      <mat-form-field class="w-70">
        <mat-label>{{ "enterDateRange" | translate }}</mat-label>
        <mat-date-range-input [rangePicker]="picker">
          <input matStartDate formControlName="date_begin" placeholder="Start date" />
          <input matEndDate formControlName="date_end" placeholder="End date" />
        </mat-date-range-input>
        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-date-range-picker #picker></mat-date-range-picker>
      </mat-form-field>
    </div>
    <div class="flex">
      <div class="w-full mb-4 lg:flex-none lg:w-64 lg:me-2">
        <mat-checkbox (change)="toggleDevices($event); refreshPoints()" color="primary" class="lg:mb-4">
          <h3 class="!mb-0">{{ "nodes" | translate }}</h3>
        </mat-checkbox>

        @for (device of devices; track device.remote_id) {
        <div>
          <mat-checkbox [(ngModel)]="device.selected" (change)="refreshPoints(); refreshSelectioState()"
            color="primary">{{ device | deviceName }}</mat-checkbox>
        </div>
        }
      </div>
      <div class="w-full mb-4 lg:flex-none lg:w-64 lg:me-2">
        <mat-checkbox (change)="toggleVariables($event)" color="primary" class="lg:mb-4">
          <h3 class="!mb-0">{{ "variables" | translate }}</h3>
        </mat-checkbox>

        @for (variable of variables; track variable.name) {
        <div>
          <mat-checkbox [(ngModel)]="variable.selected" (click)="refreshSelectioState()" color="primary">{{
            variable.full_name }}</mat-checkbox>
        </div>
        }
      </div>
      <div class="w-full mb-4 lg:flex-none lg:w-64">
        <mat-checkbox (change)="togglePoints($event)" color="primary" class="lg:mb-4">
          <h3 class="!mb-0">{{ "points" | translate }}</h3>
        </mat-checkbox>

        @if (!points.length) {
        <h4 class="ps-3">{{ "noPointsAvailable" | translate }}</h4>
        }
        @for (point of points; track point.id) {
        <div>
          <mat-checkbox [(ngModel)]="point.selected" (click)="refreshSelectioState()" color="primary">
            {{ point.thermal_cam_id }}/{{ point | pointName | async }}
          </mat-checkbox>
        </div>
        }
      </div>
      @if (displayRecognitions) {
      <div class="w-full mb-4 lg:flex-none lg:w-64">
        <mat-checkbox (change)="toggleRecognitions($event)" color="primary" class="lg:mb-4">
          <h3 class="!mb-0">{{ "recognitions" | translate }}</h3>
        </mat-checkbox>

        @for (
        recognition of recognitions;
        track recognition.id;
        let idx = $index
        ) {
        <div>
          <mat-checkbox [(ngModel)]="recognition.selected" (click)="refreshSelectioState()" color="primary">{{
            recognition.recognitionType! + "Recognition" | translate }}
            {{ recognition.uom! | translate }}
          </mat-checkbox>
        </div>
        }
      </div>
      }
    </div>
    <div [formGroup]="form">
      <h3 class="mb-2">{{ "dataOptions" | translate }}</h3>
      <div class="flex flex-wrap items-center">
        <mat-form-field class="w-full lg:flex-none lg:w-64">
          <mat-label>{{ "dataSynchronization" | translate }}</mat-label>
          <mat-select formControlName="aggregate_over">
            <mat-option [value]="5">{{ "fiveMinutes" | translate }}</mat-option>
            <mat-option [value]="30">{{
              "thirtyMinutes" | translate
              }}</mat-option>
            <mat-option [value]="60">{{ "oneHour" | translate }}</mat-option>
          </mat-select>
        </mat-form-field>
        <mat-checkbox formControlName="fill_missing" color="primary" class="lg:ms-4 mb-5">
          {{ "fillMissingValues" | translate }}
        </mat-checkbox>
      </div>
    </div>
    <div [formGroup]="form">
      <h3 class="!mb-1">{{ "dataDeliveryOptions" | translate }}</h3>
      <mat-checkbox formControlName="send_email" color="primary">
        {{ "sendDownloadLinkToEmail" | translate }}
      </mat-checkbox>
    </div>
  </div>
</div>
<mat-dialog-actions class="bg-gray-50 !px-4 !py-4 !justify-between">
  <button mat-flat-button [mat-dialog-close]>{{ "cancel" | translate }}</button>
  <button mat-flat-button [disabled]="!anySelected" (click)="export()" color="primary" cdkFocusInitial>
    {{ "exportCSV" | translate }}
  </button>
</mat-dialog-actions>