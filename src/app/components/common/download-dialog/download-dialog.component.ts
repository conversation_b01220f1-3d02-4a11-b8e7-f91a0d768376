import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import { Observable, lastValueFrom, map, of, take } from 'rxjs';
import {
  ICSVExportCameraRecognitionVars,
  ICSVExportCameraVars,
  ICSVExportRequest,
  IGetCurrentCamPoint,
  IVariableToDisplay,
} from '../../../api/api-sdk';
import { BaseComponent } from '../../../common/base.component';
import { DeviceNamePipe } from '../../../common/pipes/device-name.pipe';
import { PointNamePipe } from '../../../common/pipes/point-name.pipe';
import SharedModules from '../../../common/shared.modules';
import {
  IConfiguredDevice,
  IDevice,
  Selectable,
} from '../../../model/dashboard';

import { AdminActions } from '../../../state/admin/actions';
import { adminFeature } from '../../../state/admin/feature';

export interface IDownloadConfig {
  startDate: Date;
  endDate: Date;
  preSelectedDevices: Observable<Partial<IDevice>[]>;
  preSelectedVariables: Observable<Partial<IVariableToDisplay>[]>;
  preSelectedPoints?: Observable<Partial<IGetCurrentCamPoint>[]>;
}

export interface IRecognitionDownloadItem {
  id: string;
  recognitionType: string;
  uom: string;
}

@Component({
  selector: 'app-download-dialog',
  standalone: true,
  imports: [
    ...SharedModules,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DeviceNamePipe,
    PointNamePipe,
  ],
  templateUrl: './download-dialog.component.html',
  styleUrl: './download-dialog.component.scss',
})
export class DownloadDialogComponent extends BaseComponent implements OnInit {
  devices!: Partial<Selectable<IDevice>>[];
  variables!: Partial<Selectable<IVariableToDisplay>>[];
  points!: Partial<Selectable<IGetCurrentCamPoint>>[];
  recognitions!: Selectable<IRecognitionDownloadItem>[];

  displayRecognitions = false;

  form = new FormGroup({
    date_begin: new FormControl(),
    date_end: new FormControl(),
    aggregate_over: new FormControl<number>(0, { nonNullable: true }),
    fill_missing: new FormControl<boolean>(false, { nonNullable: true }),
    send_email: new FormControl<boolean>(false, { nonNullable: true }),
  });

  anySelected = false;

  private allDevices!: IConfiguredDevice[];
  private selectedCamerasWithRecognition!: IConfiguredDevice[];

  constructor(
    @Inject(MAT_DIALOG_DATA) private data: IDownloadConfig,
    private dialogRef: MatDialogRef<DownloadDialogComponent>,
    private store: Store,
  ) {
    super();
  }

  async ngOnInit() {
    this.allDevices = await lastValueFrom(
      this.store.select(adminFeature.selectAllDevices).pipe(take(1)),
    );

    this.form.reset({
      date_begin: this.data.startDate,
      date_end: this.data.endDate,
      aggregate_over: 5,
      send_email: true,
    });
    this.devices = await this.getSelectableList(
      this.store.select(adminFeature.selectAllDevicesForActiveLocation),
      this.data.preSelectedDevices,
      (d) => d.remote_id,
    );
    this.variables = await this.getSelectableList(
      this.store.select(adminFeature.selectVariables),
      this.data.preSelectedVariables,
      (v) => v.name,
    );
    this.refreshPoints();

    this.refreshSelectioState();
  }

  export() {
    const selectedRecognitions = this.recognitions.filter((r) => r.selected);
    let cameraRecognitionVariables: ICSVExportCameraRecognitionVars[] = [];
    if (selectedRecognitions.length) {
      cameraRecognitionVariables = this.selectedCamerasWithRecognition.map(
        (d) =>
          ({
            camera_id: d.thermal_camera_id!,
            variables: selectedRecognitions
              .map((r) =>
                ['minimum', 'average', 'maximum'].map(
                  (value) => `${r.recognitionType}_${value}_${r.uom}`,
                ),
              )
              .flat(),
          }) satisfies ICSVExportCameraRecognitionVars,
      );
    }

    const params: ICSVExportRequest = {
      ...this.form.getRawValue(),
      date_begin: dayjs(this.form.value.date_begin).format('YYYY-MM-DD'),
      date_end: dayjs(this.form.value.date_end).format('YYYY-MM-DD'),
      remote_ids: this.devices
        .filter((d) => d.selected)
        .map((d) => d.remote_id!),
      variables: this.variables.filter((v) => v.selected).map((v) => v.name!),
      camera_variables: this.points
        .filter((v) => v.selected)
        .map(
          (p) =>
            ({
              point_id: p.id!,
              camera_id: p.thermal_cam_id!,
              variables: [
                'temperature',
                'vapour_pressure_difference',
                'stomata',
              ],
            }) satisfies ICSVExportCameraVars,
        ),
      camera_recognition_variables: cameraRecognitionVariables.length
        ? cameraRecognitionVariables
        : undefined,
      aggregate: true,
    };
    this.store.dispatch(AdminActions.startExportTask({ params }));
    this.dialogRef.close();
  }

  toggleDevices(event: MatCheckboxChange) {
    this.toggleSelection(this.devices, event.checked);
  }

  toggleVariables(event: MatCheckboxChange) {
    this.toggleSelection(this.variables, event.checked);
  }

  togglePoints(event: MatCheckboxChange) {
    this.toggleSelection(this.points, event.checked);
  }

  toggleRecognitions(event: MatCheckboxChange) {
    this.toggleSelection(this.recognitions, event.checked);
  }

  async refreshPoints() {
    this.points = await this.getSelectableList(
      this.store
        .select(adminFeature.selectDevicePoints)
        .pipe(
          map((dps) =>
            dps.filter((p) =>
              this.devices.some(
                (d) => d.selected && d.thermal_camera_id === p.thermal_cam_id,
              ),
            ),
          ),
        ),
      (this.data.preSelectedPoints ?? of([])).pipe(
        map((s) => [...s, ...(this.points ?? []).filter((p) => p.selected)]),
      ),
      (p) => p.id,
    );
  }

  refreshSelectioState() {
    this.anySelected =
      this.devices.some((v) => v.selected) &&
      (this.variables.some((v) => v.selected) ||
        this.points.some((p) => p.selected) ||
        this.recognitions.some((r) => r.selected));

    this.refreshRecognitionState();
  }

  private refreshRecognitionState() {
    this.selectedCamerasWithRecognition = this.allDevices.filter(
      (confDev) =>
        !!confDev.flower_recognition &&
        this.devices.some(
          (downDev) =>
            downDev.remote_id === confDev.remote_id &&
            downDev.selected &&
            !!downDev.thermal_camera_id,
        ),
    );

    const alreadySelected = new Set(
      this.recognitions?.filter((r) => r.selected).map((r) => r.id),
    );

    this.recognitions = [];

    const recognitionTypes: Record<string, string[]> = {
      ROSE: ['flowers', 'leaves'],
      GERBERA: ['flowers', 'leaves'],
      CUCUMBER: ['flowers', 'leaves', 'fruits', 'heads'],
      TOMATO: ['flowers', 'leaves', 'fruits', 'heads'],
    };

    const uoms = ['temperature', 'vpd'];

    for (const device of this.selectedCamerasWithRecognition) {
      const types = recognitionTypes[device.flower_recognition!] || [];

      for (const recognitionType of types) {
        for (const uom of uoms) {
          const exists = this.recognitions.some(
            (r) => r.recognitionType === recognitionType && r.uom === uom,
          );

          const id = `${recognitionType}_${uom}`;

          if (!exists) {
            this.recognitions.push({
              id,
              recognitionType,
              uom,
              selected: alreadySelected.has(id),
            });
          }
        }
      }
    }

    this.displayRecognitions = this.selectedCamerasWithRecognition.length > 0;
  }

  private async getLatest<T>(stream: Observable<T[]>) {
    return await lastValueFrom(stream.pipe(take(1)));
  }

  private async getSelectableList<T>(
    stream: Observable<T[]>,
    preSelectedStream: Observable<T[]>,
    getId: (value: T) => unknown,
  ): Promise<Selectable<T>[]> {
    const list = await this.getLatest(stream);
    const preSelectedList = new Set(
      (await this.getLatest(preSelectedStream)).map((i) => getId(i)),
    );
    return list.map((i) => ({
      ...i,
      selected: preSelectedList.has(getId(i)),
    }));
  }

  private toggleSelection(
    list: Partial<Selectable<unknown>>[],
    state: boolean,
  ) {
    for (const item of list) {
      item.selected = state;
    }
    this.refreshSelectioState();
  }
}
