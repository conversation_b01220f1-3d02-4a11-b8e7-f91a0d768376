import dayjs from 'dayjs';

import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dpCalendarAgo',
  standalone: true,
})
export class DpCalendarAgoPipe implements PipeTransform {
  transform(value: dayjs.Dayjs) {
    let res = value.calendar(undefined, {
      sameDay: '[Today at] H:mm',
      nextDay: '[Tomorrow at] H:mm',
      nextWeek: 'dddd [at] H:mm',
      lastDay: '[Yesterday at] H:mm',
      lastWeek: '[Last] dddd [at] H:mm',
      sameElse: 'DD/MM/YYYY [at] H:mm',
    });
    if (value.diff(dayjs(), 'day') >= 2) {
      res += ` (${value.fromNow()})`;
    }
    return res;
  }
}
