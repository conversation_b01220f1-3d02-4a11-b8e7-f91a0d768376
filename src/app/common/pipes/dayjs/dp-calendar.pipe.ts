import dayjs from 'dayjs';

import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dpCalendar',
  standalone: true,
})
export class DpCalendarPipe implements PipeTransform {
  transform(value: dayjs.Dayjs) {
    return value.calendar(undefined, {
      sameDay: '[Today at] H:mm',
      nextDay: '[Tomorrow at] H:mm',
      nextWeek: 'dddd [at] H:mm',
      lastDay: '[Yesterday at] H:mm',
      lastWeek: '[Last] dddd [at] H:mm',
      sameElse: 'DD/MM/YYYY [at] H:mm',
    });
  }
}
