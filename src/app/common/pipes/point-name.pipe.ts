import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { map, of } from 'rxjs';
import { IGetCurrentCamPoint } from '../../api/api-sdk';

@Pipe({
  name: 'pointName',
  standalone: true,
})
export class PointNamePipe implements PipeTransform {
  constructor(private translate: TranslateService) {}

  transform(point: Partial<IGetCurrentCamPoint>) {
    const pointName = point.name || point.id;
    return pointName
      ? of(pointName)
      : this.translate
          .get('point')
          .pipe(
            map(
              (pointTitle) =>
                `${pointTitle} ${point.last_x_position}x${point.last_y_position}`,
            ),
          );
  }
}
