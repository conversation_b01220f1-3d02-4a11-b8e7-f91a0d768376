/* eslint-disable @ngrx/no-store-subscription */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { Chart, ChartConfiguration } from 'chart.js';
import {
  CameraApi,
  DataApi,
  LocationChartAllVarsRequest,
} from '../api/api-sdk';
import { ISigrowLocation } from '../model/admin';
import { ISigrowChartConfiguration, SigrowDataset } from '../model/dashboard';
import { DateRange } from '../model/dateRange';
import { adminFeature } from '../state/admin/feature';

export class BaseDashboardService {
  protected cameraApi = inject(CameraApi);
  protected dataApi = inject(DataApi);
  protected store = inject(Store);

  protected timezone: string | undefined;

  // TODO: refactor this
  protected colorsMap = new Map<string, string>();

  constructor() {
    Chart.defaults.font.family = 'Satoshi, sans-serif';
    // TODO: refactor this
    this.store
      .select(adminFeature.selectLocationTimezone)
      .subscribe((timezone) => (this.timezone = timezone));
  }

  loadReadings(activeLocation: ISigrowLocation, dateRange: DateRange) {
    const { date_begin, date_end } = this.getApiDateRange(dateRange);
    return this.dataApi.locationChartAllVarsCreate(
      activeLocation!.location!.central_id,
      new LocationChartAllVarsRequest({
        date_begin,
        date_end,
      }),
    );
  }

  getApiDateRange(
    dateRange: DateRange,
    apiTimestampFormat: string = 'YYYYMMDDHH',
  ) {
    return {
      date_begin: dateRange
        .start()
        .tz(this.timezone, !dateRange.applyStartTimezone)
        .format(apiTimestampFormat),
      date_end: dateRange
        .end()
        .tz(this.timezone, !dateRange.applyEndTimezone)
        .format(apiTimestampFormat),
    };
  }

  protected getEmptyChartData(): ISigrowChartConfiguration['data'] {
    return { datasets: [] };
  }

  protected getEmptyChartOptions(
    dateRange: DateRange,
  ): ChartConfiguration['options'] {
    const daysRange = dateRange.end().diff(dateRange.start(), 'days');

    return {
      responsive: true,
      maintainAspectRatio: false,
      elements: {
        line: {
          tension: 0.05,
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            tooltipFormat: 'dddd HH:mm:ss',
            unit: daysRange > 2 ? 'day' : 'hour',
            displayFormats: {
              millisecond: 'HH:mm:ss.SSS',
              second: 'HH:mm:ss',
              minute: 'HH:mm',
              hour: daysRange > 2 ? 'ddd HH:mm' : 'HH:mm',
              day: 'MMM DD',
              week: 'MMM DD',
              month: 'MMM YYYY',
            },
          },
          adapters: {
            date: {
              zone: this.timezone,
            },
          },
        },
      },
      animation: {
        duration: 0,
      },
      interaction: {
        intersect: false,
        mode: 'x',
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
        zoom: {
          pan: {
            enabled: true,
            modifierKey: 'ctrl',
          },
          zoom: {
            drag: {
              enabled: true,
              backgroundColor: 'rgba(158, 158, 158, 0.3)',
            },
            mode: 'xy',
          },
        },
      },
    };
  }

  protected assignDatasetColors(dataset: SigrowDataset) {
    const color = this.getDatasetColor(dataset);
    dataset.borderColor = (dataset as any).pointBackgroundColor = color;
  }

  protected getDatasetColor(dataset: SigrowDataset) {
    let color: string | undefined;

    const colorKey = `${dataset.deviceId}${dataset.label}${dataset.variableName}`;
    color = this.colorsMap.get(colorKey);
    if (!color) {
      color = this.chartColors[this.colorsMap.size];
      this.colorsMap.set(colorKey, color);
    }

    return color;
  }

  private readonly chartColors = [
    '#3F51B5',
    '#00BCD4',
    '#8BC34A',
    '#FFC107',
    '#795548',
    '#673AB7',
    '#F44336',
    '#03A9F4',
    '#4CAF50',
    '#FFEB3B',
    '#FF5722',
    '#E91E63',
    '#607D8B',
    '#2196F3',
    '#9C27B0',
    '#009688',
    '#CDDC39',
    '#FF9800',
    '#9E9E9E',
    '#36A2EB',
    '#FFCE56',
    '#4BC0C0',
    '#9966FF',
    '#FF9F40',
    '#FF004D',
    '#003366',
    '#23F890',
    '#403075',
    '#CD88AF',
    '#FFAA00',
    '#D46A6A',
    '#4F628E',
    '#877CB0',
    '#FFAA00',
    '#FFC758',
    '#3F1255',
    '#061539',
    '#440027',
    '#550000',
    '#86C98A',
    '#801515',
    '#AA5585',
    '#661141',
    '#2D8632',
    '#003133',
    '#261758',
    '#162955',
    '#54A759',
    '#236467',
    '#5A2971',
    '#882D61',
    '#AA3939',
    '#0D4A4D',
    '#2E4272',
    '#DE9400',
    '#605292',
    '#FFAAAA',
    '#004304',
    '#9974AA',
    '#270339',
    '#12073B',
    '#784A8E',
    '#417E80',
    '#116416',
    '#67989A',
    '#7887AB',
  ];
}
