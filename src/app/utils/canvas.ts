export class CanvasUtils {
  static readonly serverImageWidth = 206;
  static readonly serverImageHeight = 156;
  static readonly serverAspectRatio =
    this.serverImageWidth / this.serverImageHeight;

  static getServerCoordinatesFromMouseEvent(
    event: MouseEvent,
    clientWidth: number,
    clientHeight: number
  ) {
    return {
      serverX: Math.floor(
        event.offsetX * (this.serverImageWidth / clientWidth)
      ),
      serverY: Math.floor(
        event.offsetY * (this.serverImageHeight / clientHeight)
      ),
    };
  }
}
