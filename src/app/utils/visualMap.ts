import { ElementRef } from '@angular/core';
import { IVisualMapPoint } from '../model/node';

export class VisualMapUtils {
  static convertCoordsToStorage(
    point: IVisualMapPoint,
    image: ElementRef<HTMLImageElement>,
  ) {
    const imageRect = image.nativeElement.getBoundingClientRect();
    const vmX = point.x - imageRect.x;
    const vmY = point.y - imageRect.y;
    const coeff = image.nativeElement.naturalWidth / image.nativeElement.width;
    return {
      x: vmX * coeff,
      y: vmY * coeff,
    } satisfies IVisualMapPoint;
  }

  static convertCoordsToDisplay(
    point: IVisualMapPoint,
    image: ElementRef<HTMLImageElement>,
  ) {
    const coeff = image.nativeElement.width / image.nativeElement.naturalWidth;
    return {
      x: point.x * coeff,
      y: point.y * coeff,
    } satisfies IVisualMapPoint;
  }
}
