import { Chart, ChartEvent } from 'chart.js';
import { getRelativePosition } from 'chart.js/helpers';

export class ChartUtils {
  static getChartPointCoordinates(
    chart: Chart,
    chartEvent: {
      event?: ChartEvent | undefined;
      active?: object[] | undefined;
    },
  ) {
    const canvasPosition = getRelativePosition(
      chartEvent.event!,
      chart as never,
    );
    const chartX = chart.scales['x']?.getValueForPixel(canvasPosition.x);
    const chartY = chart.scales['y']?.getValueForPixel(canvasPosition.y);
    return { chartX, chartY };
  }
}
