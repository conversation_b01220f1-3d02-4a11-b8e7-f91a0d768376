import dayjs from 'dayjs';
import {
  DateRange,
  DateRangesPredefined,
  dateRanges,
} from '../model/dateRange';

export class DateUtils {
  static getShiftedDateRange(dateRange: DateRange, delta: number) {
    const currentStart = dateRange.start();
    const currentEnd = dateRange.end();
    const currentRangeInDays = currentEnd.diff(currentStart, 'days', false);
    const deltaMS = delta * currentRangeInDays;

    const newStart = currentStart.add(deltaMS, 'days');
    const newEnd = currentEnd.add(deltaMS, 'days');

    const now = dayjs();

    let newDateRange: DateRange;

    if (newStart.isBefore(now) && newEnd.isBefore(now)) {
      newDateRange = DateRange.fromStartEnd(newStart, newEnd);
    } else {
      let newDateRangeName: string;
      if (currentRangeInDays === 3) {
        newDateRangeName = DateRangesPredefined.last3days;
      } else if (currentRangeInDays === 7) {
        newDateRangeName = DateRangesPredefined.last7days;
      } else if (currentRangeInDays === 30) {
        newDateRangeName = DateRangesPredefined.last30days;
      } else {
        newDateRangeName = DateRangesPredefined.last24hours;
      }
      newDateRange = dateRanges.find((dr) => dr.name === newDateRangeName)!;
    }

    return newDateRange;
  }
}
