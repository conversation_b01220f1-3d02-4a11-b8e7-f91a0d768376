import pako from 'pako';
import { IDashboardState } from '../components/admin/dashboard/state/dashboard.feature';
import { IChartReportData, IDashboardConfig } from '../model/dashboard';

export class DashboardUtils {
  static getDashboardConfigFromState(state: IDashboardState): IDashboardConfig {
    return {
      customPoints: state.customPoints,
      devices: state.devices,
      points: state.points,
      recognitions: state.recognitions,
      recognitionVariables: state.recognitionVariables,
      pointVariables: state.pointVariables,
      variables: state.variables,
      chartUomConfigs: state.chartUomConfigs,
    };
  }

  static compact(obj: any): any {
    if (obj === null || obj === undefined) {
      return undefined;
    }

    if (typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      const cleanedArray = obj
        .map((item) => this.compact(item))
        .filter((item) => item !== undefined);

      return cleanedArray.length > 0 ? cleanedArray : undefined;
    }

    const result: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = this.compact(obj[key]);
        if (value !== undefined) {
          result[key] = value;
        }
      }
    }

    return result;
  }

  static encodeChartReportDataUri(
    centralId: number,
    startTimestamp: string,
    endTimestamp: string,
    state: IDashboardState,
  ) {
    const data: IChartReportData = {
      centralId,
      startTimestamp,
      endTimestamp,
      config: DashboardUtils.getDashboardConfigFromState(state),
    };
    const dataCompact = DashboardUtils.compact(data);
    const jsonString = JSON.stringify(dataCompact);

    const compressed = pako.deflate(jsonString);

    let binaryString = '';
    compressed.forEach((byte) => {
      binaryString += String.fromCharCode(byte);
    });

    const base64 = btoa(binaryString);

    return encodeURIComponent(base64);
  }

  static decodeChartReportDataFromUri(uri: string): IChartReportData {
    const decoded = decodeURIComponent(uri);
    const binaryString = atob(decoded);

    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const decompressed = pako.inflate(bytes, { to: 'string' });

    return JSON.parse(decompressed) as IChartReportData;
  }
}
