import { IHeatmapConfigs } from '../api/api-sdk';
import { IDevice } from './dashboard';

export enum DeviceValueType {
  min,
  max,
}

export interface IHeatmapDevice {
  device: IDevice;
  temperature: number;
  timestamp: number;
  valueType: DeviceValueType | undefined;
}

export interface IHeatmap {
  cols: number;
  devices: IHeatmapDevice[];
  temperatures: number[][];
  colorscale: Plotly.ColorScale | undefined;
}

export interface IHeatmapConfigRow {
  devices: (IDevice | undefined)[];
}

export interface IHeatmapGridConfig extends IHeatmapConfigs {
  heatmap_data: {
    rows: IHeatmapConfigRow[];
  };
}

export const emptyHeatmap: IHeatmap = {
  cols: 0,
  devices: [],
  temperatures: [],
  colorscale: undefined,
};
