@import "@angular/material/prebuilt-themes/indigo-pink.css";

@import "ag-grid-community/styles/ag-grid.css";
@import "ag-grid-community/styles/ag-theme-material.css";

html,
body {
  height: 100%;
}

.sidenav,
.mat-drawer {
  background: #fafafa !important;
}

.mat-sidenav-content,
.mat-drawer-container {
  background-color: transparent !important;
}

.mat-content.mat-content-hide-toggle {
  margin-right: 0 !important;
}

.mat-mdc-dialog-container {
  max-width: 90vw !important;
  max-height: 90vh !important;
}

.mat-mdc-dialog-content {
  max-height: 80vh !important;
}

.ag-theme-material {
  --ag-header-background-color: #fafafa;
  --ag-font-family: Satoshi, sans-serif;
  --ag-font-size: 14px;
}

.mat-mdc-chip.mat-mdc-chip-option.mat-mdc-chip-selected {
  --mdc-chip-label-text-color: #fff !important;
  --mdc-chip-with-icon-selected-icon-color: #fff !important;
  --mdc-chip-label-text-weight: 500 !important;
}

.mat-expansion-panel-body {
  padding: 0 !important;
}

.mat-badge-content {
  color: #fff !important;
  padding-top: 3px;
  font-size: 18px !important;
}

.filled {
  font-variation-settings: "FILL" 1;
}

.mdc-button__label {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cdk-overlay-container {
  z-index: 10000000000 !important;
}

body.plt-mobile {
  .app-container {
    top: 56px;
  }

  .fabs {
    opacity: 50%;
  }

  .mat-mdc-menu-panel {
    max-width: 90vw !important;
  }
}

.cameras-drawer {
  .mat-drawer-inner-container {
    overflow-x: hidden !important;
    overflow-y: scroll !important;
  }
}
