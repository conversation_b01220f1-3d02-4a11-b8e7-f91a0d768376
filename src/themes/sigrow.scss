@use "@angular/material" as mat;

@include mat.core();

$sigrow-primary-palette: (
  50: #b1e2f9,
  100: #7ecff5,
  200: #4cbcf0,
  300: #26aeed,
  400: #00a0ea,
  500: #0091db,
  600: #007fc7,
  700: #006fb3,
  800: #004f91,
  900: #004f91,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: rgba(black, 0.87),
    600: rgba(black, 0.87),
    700: rgba(black, 0.87),
    800: rgba(black, 0.87),
    900: rgba(black, 0.87),
  ),
);

$sigrow-accent-palette: (
  50: #dff2ed,
  100: #b2dfd0,
  200: #81cab2,
  300: #50b595,
  400: #2ba580,
  500: #2ba580,
  600: #08956d,
  700: #058862,
  800: #007853,
  900: #006846,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: rgba(black, 0.87),
    600: rgba(black, 0.87),
    700: rgba(black, 0.87),
    800: rgba(black, 0.87),
    900: rgba(black, 0.87),
  ),
);

$sigrow-primary: mat.define-palette($sigrow-primary-palette, 500);
$sigrow-accent: mat.define-palette($sigrow-accent-palette, 500);
$sigrow-typography: mat.define-typography-config(
  $font-family: "Satoshi, sans-serif",
);

$sigrow-theme: mat.define-light-theme(
  (
    color: (
      primary: $sigrow-primary,
      accent: $sigrow-accent,
    ),
    typography: $sigrow-typography,
    density: 0,
  )
);

@include mat.all-component-themes($sigrow-theme);

.bg-primary {
  background-color: #0091db !important;
}

.bg-accent {
  background-color: #2ba580 !important;
}

app-gauge {
  .reading-block {
    font-size: 34px !important;
    transform: translateY(74px) !important;
  }

  .dli {
    .reading-block {
      font-size: 24px !important;
    }
  }
}
