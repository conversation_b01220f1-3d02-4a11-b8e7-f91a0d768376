<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>SIGROW</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,600,0..1,200"
    />
  </head>

  <!-- Google tag (gtag.js) -->
  <script
    async
    src="https://www.googletagmanager.com/gtag/js?id=G-LWF6N76FHP"
  ></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-LWF6N76FHP");
  </script>

  <!-- Contact us -->
  <script
    type="text/javascript"
    src="https://euc-widget.freshworks.com/widgets/80000002950.js"
    async
    defer
  ></script>
  <script>
    window.fwSettings = {
      widget_id: 80000002950,
    };
    !(function () {
      if ("function" != typeof window.FreshworksWidget) {
        var n = function () {
          n.q.push(arguments);
        };
        (n.q = []), (window.FreshworksWidget = n);
      }
    })();
  </script>

  <body>
    <app-root></app-root>
  </body>
</html>
