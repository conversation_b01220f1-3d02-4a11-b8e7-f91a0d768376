{"close": "Close", "slide": "Slide", "previous": "Previous", "next": "Next", "selectMonth": "Select month", "selectYear": "Select year", "previousMonth": "Previous month", "nextMonth": "Next month", "first": "First", "last": "Last", "hh": "HH", "hours": "Hours", "mm": "MM", "minutes": "Minutes", "incrementHours": "Increment hours", "decrementHours": "Decrement hours", "incrementMinutes": "Increment minutes", "decrementMinutes": "Decrement minutes", "ss": "SS", "seconds": "Seconds", "incrementSeconds": "Increment seconds", "decrementSeconds": "Decrement seconds", "ID": "ID", "nodeName": "Device name", "date": "Date", "noResultsFound": "No Results Found", "automatic": "Automatic", "max": "Max", "min": "Min", "thermalCamera": "Thermal Camera", "from": "From", "about": "About", "sigrowAddress": "Sigrow B.V. Nieuwe Kazernelaan 2D86, Ede-Wageningen, The Netherlands", "camera": "Camera", "view": "View", "editPoints": "Edit points", "configurationFor": "Configuration for", "timezone": "Timezone", "nodesStatusWarningAlerts": "Nodes status warning alerts", "statusAlerts": "Status alerts warn you about potential problems with your nodes (low battery, low radio signal) <strong>on a daily basis.</strong><br> Set to 'disabled' to disable the status warning alerts for all centrals and all nodes assigned to your account.", "nodesOfflineAlerts": "Nodes offline alerts", "nodesOfflineAlertsWarn": "Nodes offline alerts warn you about nodes that", "apiv2Key": "APIv2 key", "generate": "Generate", "regenerate": "Regenerate", "youCanGenerateAPIv2Key": "You can generate APIv2 key to be used with Sigrow APIv2.", "resetApplication": "Reset application", "thisActionClearsCache": "This action clears the application cache. Your data will not be affected. It's best to use this function when instructed by the support.", "nodeMapSize": "Map Size", "statusAlertsWarn": "Status alerts warn you about potential problems with your nodes (low battery, low radio signal) on a daily basis. Set to 'off' to disable the status warning alerts for all nodes assigned to <strong>central</strong>.", "nodesOfflineAlertsWarnAll": "Nodes offline alerts warn you about nodes that haven't uploaded any data in last 2 hours. Set to 'off' to disable the nodes offline alerts for all nodes assigned to central.", "subscribeReadingsNotifications": "You can subscribe to readings alert notifications. If any of your sensors within the current installation will report a reading that isn't withing the minimum and maximum range, you will be notified via selected channel (email, sms). It's also possible to insert only one of minimum or maximum.", "noSensorsWithinInstallation": "If no sensors within the current installation has a reading outside of the declared range, no notification will be sent.", "sentHourly": "Please note that the readings alert notifications are being send hourly between 6AM and 8PM UTC.", "smsAlerts": "<strong>SMS alerts can only be enabled for premium users.</strong> Please reach out to us if you'd like to use this functionality.", "alertEmails": "Alert e-mails", "alertPhone": "Alert phone", "average": "Average", "treshold": "<PERSON><PERSON><PERSON><PERSON>", "canopyVPDLeafLevel": "Canopy VPD Leaf Level", "whyVPDmatter": "Why does plant VPD matter? It signals plant balance. High VPD can cause water stress and low VPD can hinder transpiration and calcium uptake. Moderate VPD can stimulate photosynthesis and plant growth, leading to higher yields.", "avoidYieldLoss": "<strong> ✓ Avoid yield loss due to stomata closure:</strong> By monitoring your plants' VPD, you can take proactive measures to prevent stress and optimize your yields. High VPD can increase transpiration rates and water stress in plants, which can negatively affect growth and yield. By keeping VPD levels moderate, you can stimulate photosynthesis leading to higher yields in most crops.", "saveEnergy": "<strong>✓ Save energy by growing at higher humidity levels:</strong> While at the VPD balance range, you can delay windows opening, save energy, and avoid plant condensation, while still promoting healthy plant growth. This can help you achieve more efficient and cost-effective plant growth, while lowering the risk of fungal diseases.", "discoverMore": "<strong>✓Discover more</strong> from scientific research:", "avaporPressure": "Avapor pressure deficit effect on crop canopy photosynthesis by <PERSON><PERSON><PERSON><PERSON>", "minimizingVPDFluctuations": "Minimizing VPD Fluctuations Maintains Higher Stomatal Conductance and Photosynthesis, Resulting in Improvement of Plant Growth in Lettuce - <PERSON><PERSON><PERSON> et al.", "steeringFogging": "Steering of Fogging: Control of Humidity, Temperature or Transpiration? <PERSON><PERSON> and <PERSON><PERSON>, Wageningen UR Greenhouse Horticulture", "vPDcalculator": "You can measure your plant's VPD using various tools, such as a psychrometer, a digital VPD calculator, or a data logger equipped with temperature and relative humidity sensors.", "additionalResources": "Additional Resources:", "vPD_selection_Crop": "Crop Production Science in Horticulture 27, Heuvelink, Ep - Tomatoes, CABI (2018)", "steering": "Steering of Fogging: Control of Humidity, Temperature or Transpiration", "plantResponsesVapor": "Plant responses to rising vapor pressure deficit", "vaporPressureDeficit": "A vapor pressure deficit effect on crop canopy photosynthesis", "configurationForUser": "Configuration for this user", "stomataClosureRisk": "Stomata Closure Risk", "noCropCondensation": "No Crop Condensation", "cropCondensationRisk": "Crop Condensation Risk", "optimalStomataOpening": "Optimal Stomata Opening", "forgotPassword": "Forgot password", "emailAndPassword": "Enter your email and your password will be reset and emailed to you.", "resetPassword": "Reset password", "backToLogin": "Back to Login", "welcome": "Welcome to SIGROW", "newEraAgrosensing": "Sense. Discover. Improve", "emailRequired": "Email required", "passwordRequired": "Password required", "rememberMe": "Remember me", "login": "<PERSON><PERSON>", "loggingIn": "Logging in...", "needHelp": "Support", "browserNotSupported": "Your browser is not supported, please log in with a different browser. If you are using the private mode, please try browsing in normal mode.", "refresh": "Refresh", "uploaded": "uploaded", "point": "Point", "attributes": "Attributes", "readings": "Table", "stomataCamera": "Stomata Camera", "pointTemperature": "Point Temperature", "csvDataExport": "CSV Data Export", "selectDateRanges": "Select date ranges, devices, and parameters to export.", "to": "To", "nodes": "Devices", "checkAll": "Check All", "variables": "Variables", "cameraPointVariables": "Camera Point Variables", "pleaseSelectNodesCameras": "Please select nodes with cameras first", "fiveMinutes": "5 minutes", "thirtyMinutes": "30 minutes", "oneHour": "1 hour", "dataDeliveryOptions": "Data Delivery Options", "additionalEmails": "Additional Emails", "exportCSV": "Export CSV", "processing": "Processing, please wait...", "hint": "Hint: You can close the window, and the export will be delivered to your email as soon as it's ready", "downloadReady": "Your download is ready", "exportFile": "Click here to download the export file.", "createAnotherExport": "Create another export", "anError": "An error occurred", "teamNotified": "The IT team has been notified. Apologies for the inconvenience.", "nodeDetailed": "Node Detailed Info", "remoteIDdevice": "Device ID", "visibility": "Visibility", "location": "Location", "indoorOutdoor": "In/Out", "place": "Place", "viewMap": "View Map", "alertsConfiguration": "Alerts Configuration", "seeConfiguration": "See also configuration on central and account level", "nodeStatusWarningAlerts": "Node Status Warning Alerts", "nodeOfflineAlerts": "Node Offline Alerts", "hairline": "Hairline", "minAverageMax": "Min/Avg/Max", "downloadTheData": "You can download the data that's currently visible in the charts as CSV.", "fillMissingValues": "Fill Missing Values", "howWork": "How does this function work?", "noEmptyValues": "It ensures that there are no empty values in your CSV file. If a particular reading is missing in a given time span, it gets filled with the most recent available reading.", "downloadAsCSV": "Download as CSV", "asPNG": "As PNG", "asCSV": "As CSV", "downloadTheDataAsPNG.": "You can download the data that's currently visible in the charts as PNG.", "downloadAsPNG": "Download as PNG", "cancel": "Cancel", "apply": "Apply", "newPassword": "New Password", "pleaseEnterUsernamePassword": "Please enter your username and the new password", "wantToKnowMore": "Want to know more about this?", "copyright": "®", "message": "Message", "persistedPointWorks": "Persisted-point works!", "sensors": "Sensors", "cameras": "Cameras", "oneDay": "1 day", "oneWeek": "1 week", "oneMonth": "1 month", "slideOf": "Slide of", "changeChartOptions": "Change Chart Options", "exportDataToCSV": "Export Data to CSV", "configuration": "Configuration", "stomataCameraSensor": "Stomata Camera is associated with this device", "exportDataPlot": "Export Data from the Plot", "selectCameraVariables": "Select camera variables", "pointName": "Point name (max 16 chars, optional)", "search": "Search...", "repeatPassword": "Repeat password", "password": "Password", "email": "Email", "noAPIKeyGenerated": "No API key generated", "addPhone": "Add phone", "addTimezone": "Add timezone", "addEmail": "Add email", "maxNumberEmails": "Max 5 emails", "batt": "Battery", "par": "PAR", "dli": "Day Light Integral", "humid": "Relative Humidity", "co2": "CO2", "g_water": "Absolute humidity", "pixel_object1_temp": "Pixel Leaf Temperature", "vbat": "Battery percentage", "temp": "Air Temperature", "soil_hum": "S Humidity", "pore_ec": "S Pore EC", "soil_temp": "S Temperature", "t_dew": "Dew Point Temperature", "t_leaf": "Dry Leaf Temp", "vpd": "VPD Air", "vpd_plant": "VPD Plant", "vpd_leaf": "VPD Leaf", "weightTotal": "Weight Total", "nodesMap": "Map", "charts": "Charts", "discoveries": "Discoveries", "timelapse": "Timelapse", "stomataCameras": "Stomata Cameras", "readingsNav": "Readings", "help": "Help", "logout": "Logout", "lastReadingFor": "Last reading for", "sendMessageSupport": "Send message to support", "chartConfigurationTitle": "Chart Configuration", "time": "Time", "fillBackgroundWithWhiteColor": "Fill background with white color", "dataSynchronization": "Data synchronization over period:", "dateRanges": "Please select the date ranges, remote sensors and parameters to be exported.", "locationName": "Location Name", "disabled": "Disabled", "enabled": "Enabled", "dataOptions": "Data Options", "sendTheData": "Send the data download link also to my email:", "nodeDetailedInfo": "Node detailed info", "changePassword": "Change password", "soil_ec": "S Bulk EC", "rssi": "Radio Strength", "par_top": "PAR Top", "par_bottom": "PAR Bottom", "rad_top": "Net Radiation", "rad_bottom": "Radiation Bottom", "soil_bec_cm": "S BULK EC", "soil_temp_f": "S Temp F", "temp_f": "Air Temp F", "weight_t": "Weight Total", "weight_1": "w1", "weight_2": "w2", "weight_3": "w3", "weight_4": "w4", "wli": "Week Light Integral", "flow_in": "Flow in", "flow_out": "Flow out", "pixel_ambient_temp": "Pixel Ambient Temperature", "e_sw_up": "Pyranometer [SW] Irradiance up", "e_sw_down": "Pyranometer [SW] Irradiance down", "e_lw_up": "Pyrgeometer [LW] Irradiance up", "e_lw_down": "Pyrgeometer [LW] Irradiance down", "e_net": "Net Radiation", "language": "Language", "moveNode": "Move device", "error": "Error", "warning": "Warning", "messageSent": "Message sent", "errorSendingMessage": "Error sending message", "dataSavedSuccessfully": "Data saved successfully", "errorOccurred": "An error has occurred saving the data", "sureReset": "Are you sure you want to reset the application settings? You will be logged out, but your data won't be affected.", "apiKeyGenereated": "The APIv2 key has been generated!", "apiKeyReGenereated": "The APIv2 key has been regenerated!", "unexpectedError": "An unexpected error occurred. Please try again later.", "emailSent": "An email has been sent to your address", "emailDoesNotExist": "The email does not exist", "noDataForLocation": "There is no data for this location.", "emailPassIncor": "The email or password is incorrect", "selectNodeToMove": "Please, select the node to move", "moveCancelled": "Move cancelled", "positionsChangedSuccessfully": "Positions changed successfully!", "errorPositionsChanged": "An error occurred while changing the positions.", "noSensorsAssociated": "There are no sensors associated with this location.", "movingNode": "Moving device ", "selectTheNewPosition": ". Please select the new position after another device.", "after": "after", "nodeSameName": "There is already a device with the same name. Please change the device name and try again.", "inactiveNodeDisplay": "An inactive device will no longer be displayed on the device map. Would you like to continue?", "passwordChanged": "Your password has been changed.", "invalidEmailExpired": "Invalid username or expired email.", "errorOcurredTryLater": "An error has ocurred, please try it later", "pointSavedSuccessfully": "Point saved successfully!", "noActiveLocations": "There are no active locations for this user.", "emailExists": "The email already exists", "invalidEmail": "The email is not valid", "selectOneRemote": "Please select at least one remote.", "selectOneVariable": "Please select at least one variable.", "preparingForExport": "Preparing file for export.", "readyForDownload": "Export file ready for download.", "errorPreparingForExport": "Error preparing file for export.", "percent": "Percent", "weight": "Weight", "flowLiters": "Flow (liters)", "radiation": "Radiation", "temperature": "Temperature", "stomataConductance": "Stomata Conductance", "addNewStomata": "Please, contact me to add a new stomata camera", "stomataOpeningRisk": "Stomata Opening Risk", "leafCondensationRisk": "Leaf Condensation Risk", "rtrPlot": "RTR Plot", "airUniformity": "Air Uniformity", "parUniformity": "PAR Uniformity", "regenerateSure": "Are you sure you want to regenerate the APIv2 key? The existing APIv2 key will be invalidated. If you have any applications using this key, they may stop working.", "send": "Send", "indoor": "Indoor", "outdoor": "Outdoor", "hidden": "Hidden", "visible": "Visible", "rearrangeNodesPositions": "Rearrange nodes positions", "remote_id": "Remote ID", "maxNumberAdditional": "Max number of additional Emails is 5", "sensorReadingsAlerts": "Sensor readings alerts", "offlineAlerts": "Nodes offline alerts warn you about nodes that", "offlineAlerts2": "haven't uploaded any data in last 2 hours.", "offlineAlerts3": "Set to 'disabled' to disable the nodes offline alerts for all centrals and all nodes assigned to your account.", "requestNewCamera": "Request new camera", "editPointsCamera": "Edit points for Camera", "addNewPoint": "Add a new point", "save": "Save", "edit": "Edit", "pointNameMax": "Point name (max 16 chars, optional)", "avgLeafTemperature": "Avg Leaf Temperature", "avgVPDPlant": "Avg VPD Plant", "avgStomataConductance": "Avg Stomata Conductance", "avgLeafTemperatureTooltip": "Avg Leaf Temperature", "avgVPDPlantTooltip": "Avg VPD Plant", "avgStomataConductanceTooltip": "Avg Stomata Conductance", "faults": "Faults", "minValues": "Min Values", "maxValues": "Max Values", "averageValues": "Average Values", "leafTemperature": "Leaf Temperature", "stomata": "stomata", "merged": "merged", "temperature+": "temperature+", "stomata+": "stomata+", "apiv3Key": "APIv3 key", "youCanGenerateAPIv3Key": "You can generate APIv3 key to be used with Sigrow APIv3.", "flowersRecognition": "Flowers", "leavesRecognition": "Leaves", "legend": "Legend", "selectDeviceAndVariable": "Select devices and variables to display", "selectDateRange": "Select date range", "enterDateRange": "Enter a date range", "last24hours": "- 24 H", "last7days": "- 7 D", "last30days": "- 30 D", "last3days": "- 3 D", "cameraTemperature": "Cam <PERSON>", "cameraVPD": "Cam VPD", "yAxisConfig": "Y-axis configuration", "maxValue": "Y Max", "minValue": "Y <PERSON>", "clear": "Clear", "confirm": "Confirm", "points": "Points", "noPointsAvailable": "No points available", "sendDownloadLinkToEmail": "Send a link to download data to your email?", "areYouSure": "Are you sure?", "pleaseConfirmAction": "Please confirm your action", "userSettings": "User settings", "locationSettings": "Location settings", "mapWidth": "Map Width", "pointConfiguration": "Point configuration", "xCoordinate": "X", "yCoordinate": "Y", "collapseCameraView": "Collapse camera view", "expandCameraView": "Expand camera view", "transpiration": "Transpiration", "addNewPointHint": "You can click on the camera image to add a new persistent point", "pointVPD": "Point VPD", "pointStomata": "Point Stomata", "biomass": "Biomass", "sort": "Sort", "export": "Export", "user": "User", "refreshChart": "Refresh chart", "favorite": "Favorite", "centralId": "Central ID", "chartPointDetails": "Chart point details", "timestamp": "Timestamp", "variable": "Variable", "reading": "Reading", "leaveComment": "Leave a comment", "comments": "Comments", "displayOnChart": "Display on chart", "deleteComment": "Delete comment", "visualMap": "Visual Map", "name": "Name", "delete": "Delete", "dashboardConfigurations": "Dashboard Configurations", "savedDashboards": "Saved dashboards", "unsetAsDefault": "Unset as default", "setAsDefault": "Set as default", "update": "Update", "saveCurrentDashboard": "Save current dashboard", "mapColumnsCount": "Map columns count", "fruitsRecognition": "Fruits", "headsRecognition": "Heads", "heatmap": "Heatmap", "resetZoom": "Reset zoom", "addChartComment": "Add chart comment", "download": "Download", "recognitions": "Recognitions", "maximum": "Maximum", "minimum": "Minimum", "rangeDetails": "Min/Avg/Max values for current date range", "contactSupport": "Contact Support", "lastMessage": "Last message", "custom": "Custom", "enableGauge": "Enable Gauge", "chooseGaugeVariable": "Choose gauge variable", "selectCommentPoint": "Select comment point", "cameraName": "Camera name", "minimizeComments": "Minimize comments", "default": "<PERSON><PERSON><PERSON>", "campTemperature": "Temperature", "camVpd": "VPD", "toggleCondensationAnalysis": "Toggle condensation analysis"}