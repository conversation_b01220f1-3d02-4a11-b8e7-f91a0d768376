image: node:18.20.0

stages:
  - dependencies
  - build
  - deploy

install_dependencies:
  stage: dependencies
  only:
    - main
    - test
  cache:
    key: ${CI_COMMIT_REF_SLUG}-node_modules  # Unique cache per branch
    paths:
      - node_modules/
    policy: pull-push  # Create or update cache in this job
  script:
    - echo "Running Deployment Stage 1 (install_dependencies) on Node 18.20, on $(hostname) at $(pwd)..."
    - NODE_OPTIONS="--max-old-space-size=4096" npm ci -f

build_project:
  stage: build
  only:
    - main
    - test
  cache:
    key: ${CI_COMMIT_REF_SLUG}-node_modules  # Use the same cache key
    paths:
      - node_modules/
    policy: pull  # Only use cache, don’t recreate it
  script:
    - echo "Running Deployment Stage 2 (npm run build) on Node 18.20..."
    # Remove dist/ folder if already exists, to ensure no left over files remain from previous build
    - rm -rf dist/
    - npm run build.test
  artifacts:
    paths:
      - dist/  # Specify the directory to keep for the next stage
    expire_in: 15 minutes  # Set artifact expiration to 15 minutes

# deploy_to_production:
#   stage: deploy
#   only:
#     - nonexistent-branch-to-be-defined       # Disable temporarily 
#   cache:
#     key: ${CI_COMMIT_REF_SLUG}-node_modules  # Use the same cache key
#     paths:
#       - node_modules/
#     policy: pull  # Only use cache, don’t recreate it
#   dependencies:
#     - build_project  # Ensures artifacts from build_project are available
#   before_script:
#     - echo "Running Deployment Stage 3 (deploy_to_remote) on Node 18.20..."
#     - echo "User $(whoami) at $(hostname) - PWD $(pwd) "
#     - echo "Prepare SSH Private Key "
#     - mkdir -p ~/.ssh
#     - echo "$PRODUCTION_DEPLOY_KEY" | tr -d '\r' > /root/.ssh/id_ed25519
#     - chmod 600 /root/.ssh/id_ed25519
#     - ssh-keyscan -H ********** >> /root/.ssh/known_hosts
#     - ssh -i /root/.ssh/id_ed25519 deploy@********** 'echo SSH connection successful'
#     - echo "Ensure rsync is available on runner "
#     - apt-get update && apt-get install -y rsync
#     - ssh deploy@********** 'mkdir -p /home/<USER>/webapp3'
#   script:
#     # Ensure deployment either succeeds 100% or is not applied and that files that do not exist on source are removed from destination:
#     # - rsync -av --chown=deploy:deploy --delete  --atomic dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3
#     - rsync -av --chown=deploy:deploy --delete dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3_tmp
#     - ssh deploy@********** 'mv /home/<USER>/webapp3 /home/<USER>/webapp3_backup && mv /home/<USER>/webapp3_tmp /home/<USER>/webapp3'
#     - ssh deploy@********** 'rm -rf /home/<USER>/webapp3_backup'
#     - rsync -av --chown=deploy:deploy  docker-compose.yml deploy@**********:/home/<USER>/
#     - rsync -av --chown=deploy:deploy  nginx.conf  deploy@**********:/home/<USER>/
#     - ssh deploy@********** 'docker compose -f /home/<USER>/docker-compose.yml up -d --force-recreate'
#   environment:
#     name: production

deploy_to_dev:
  stage: deploy
  only:
    - main
  cache:
    key: ${CI_COMMIT_REF_SLUG}-node_modules  # Use the same cache key
    paths:
      - node_modules/
    policy: pull  # Only use cache, don’t recreate it
  dependencies:
    - build_project  # Ensures artifacts from build_project are available
  before_script:
    - echo "Running Deployment Stage 3 (deploy_to_remote) on Node 18.20..."
    - echo "User $(whoami) at $(hostname) - PWD $(pwd) "
    - echo "Prepare SSH Private Key "
    - mkdir -p ~/.ssh
    - echo "$PRODUCTION_DEPLOY_KEY" | tr -d '\r' > /root/.ssh/id_ed25519
    - chmod 600 /root/.ssh/id_ed25519
    - ssh-keyscan -H ********** >> /root/.ssh/known_hosts
    - ssh -i /root/.ssh/id_ed25519 deploy@********** 'echo SSH connection successful'
    - echo "Ensure rsync is available on runner "
    - apt-get update && apt-get install -y rsync
    - ssh deploy@********** 'mkdir -p /home/<USER>/webapp3'
  script:
    # Ensure deployment either succeeds 100% or is not applied and that files that do not exist on source are removed from destination:
    # - rsync -av --chown=deploy:deploy --delete  --atomic dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3
    - rsync -av --chown=deploy:deploy --delete dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3_tmp
    - ssh deploy@********** 'mv /home/<USER>/webapp3 /home/<USER>/webapp3_backup && mv /home/<USER>/webapp3_tmp /home/<USER>/webapp3'
    - ssh deploy@********** 'rm -rf /home/<USER>/webapp3_backup'
    - rsync -av --chown=deploy:deploy docker-compose.yml deploy@**********:/home/<USER>/
    - rsync -av --chown=deploy:deploy nginx.conf deploy@**********:/home/<USER>/
    - ssh deploy@********** 'docker compose -f /home/<USER>/docker-compose.yml up -d --force-recreate'
  environment:
    name: development

deploy_to_test:
  stage: deploy
  only:
    - test
  cache:
    key: ${CI_COMMIT_REF_SLUG}-node_modules  # Use the same cache key
    paths:
      - node_modules/
    policy: pull  # Only use cache, don’t recreate it
  dependencies:
    - build_project  # Ensures artifacts from build_project are available
  before_script:
    - echo "Running Deployment Stage 3 (deploy_to_remote) on Node 18.20..."
    - echo "User $(whoami) at $(hostname) - PWD $(pwd) "
    - echo "Prepare SSH Private Key "
    - mkdir -p ~/.ssh
    - echo "$PRODUCTION_DEPLOY_KEY" | tr -d '\r' > /root/.ssh/id_ed25519
    - chmod 600 /root/.ssh/id_ed25519
    - ssh-keyscan -H ********** >> /root/.ssh/known_hosts
    - ssh -i /root/.ssh/id_ed25519 deploy@********** 'echo SSH connection successful'
    - echo "Ensure rsync is available on runner "
    - apt-get update && apt-get install -y rsync
    - ssh deploy@********** 'mkdir -p /home/<USER>/webapp3'
  script:
    # Ensure deployment either succeeds 100% or is not applied and that files that do not exist on source are removed from destination:
    # - rsync -av --chown=deploy:deploy --delete  --atomic dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3
    - rsync -av --chown=deploy:deploy --delete dist/sigrow-admin-portal/browser/ deploy@**********:/home/<USER>/webapp3_tmp
    - ssh deploy@********** 'mv /home/<USER>/webapp3 /home/<USER>/webapp3_backup && mv /home/<USER>/webapp3_tmp /home/<USER>/webapp3'
    - ssh deploy@********** 'rm -rf /home/<USER>/webapp3_backup'
    - rsync -av --chown=deploy:deploy docker-compose.yml deploy@**********:/home/<USER>/
    - rsync -av --chown=deploy:deploy nginx.conf deploy@**********:/home/<USER>/
    - ssh deploy@********** 'docker compose -f /home/<USER>/docker-compose.yml up -d --force-recreate'
  environment:
    name: development
