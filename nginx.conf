events {
    worker_connections 1024;
}

http {

    include mime.types;  # Include MIME type definitions
    default_type application/octet-stream;

    server {
        listen 80;
        server_name app3-dev.sigrow.com;

        root /usr/share/nginx/html;
        index index.html;

        location / {
            # Try to serve the exact file or directory; if not found, redirect to root
            try_files $uri $uri/ /index.html @redirect_to_root;
        }

        # Named location to handle redirection to /
        location @redirect_to_root {
            return 302 /;
        }

        # Handle static file types with caching
        # location ~* \.(css|js|jpg|png|gif|ico|svg|woff|woff2|ttf|otf)$ {
        #     expires 1y;
        #     add_header Cache-Control "public";
        # }

        # Disable access logging for static files
        location ~* \.(css|js|jpg|png|gif|ico|svg|woff|woff2|ttf|otf)$ {
            access_log off;
        }
    }
}