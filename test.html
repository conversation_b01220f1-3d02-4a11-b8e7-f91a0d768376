<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Heatmap with Custom Color Map</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  </head>
  <body>
    <div id="heatmap"></div>
    <script>
      const temperatures = [
        [30, 32, 35],
        [28, 25, 27],
        [29, 31, 33],
      ];

      // Define the custom color map
      let colorscale = [
        [0, "blue"],
        [0.5, "purple"],
        [1, "red"],
      ];

      // Create the heatmap
      let heatmapData = [
        {
          z: temperatures,
          zsmooth: "best",
          type: "heatmap",
          colorscale: colorscale,
        },
      ];

      let layout = {
        title: "Heatmap with Custom Color Map (blue-purple-red)",
        xaxis: {
          title: "X Axis",
        },
        yaxis: {
          title: "Y Axis",
        },
      };

      Plotly.newPlot("heatmap", heatmapData, layout);
    </script>
  </body>
</html>
